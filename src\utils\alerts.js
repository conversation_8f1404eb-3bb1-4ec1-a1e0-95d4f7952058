import Swal from 'sweetalert2';

// Configurações padrão para todos os alertas
const defaultConfig = {
  background: '#ffffff',
  color: '#333333',
  customClass: {
    popup: 'swal-clean-theme',
    title: 'swal-clean-title',
    htmlContainer: 'swal-clean-content',
    confirmButton: 'swal-clean-button',
    cancelButton: 'swal-clean-cancel-button'
  },
  buttonsStyling: false
};

// 1. Alerta de loading/aguarde (padrão roxo)
export const showLoadingAlert = (title = '⏳ Aguarde', text = 'Processando...') => {
  return Swal.fire({
    ...defaultConfig,
    title,
    html: `
      <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px; border-radius: 10px; margin: 10px 0;">
        <div style="font-size: 16px; font-weight: 500;">${text}</div>
      </div>
      <div style="color: #666; margin-top: 15px; font-size: 14px;">
        Aguarde enquanto processamos sua solicitação
      </div>
      <div style="margin-top: 15px;">
        <div class="loading-spinner"></div>
      </div>
    `,
    allowOutsideClick: false,
    allowEscapeKey: false,
    showConfirmButton: false,
    didOpen: () => {
      Swal.showLoading();
    }
  });
};

// 2. Alerta de sucesso (padrão verde)
export const showSuccessAlert = (title = '✅ Sucesso!', message = 'Sua solicitação foi processada com sucesso!') => {
  return Swal.fire({
    ...defaultConfig,
    title,
    html: `
      <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 15px; border-radius: 10px; margin: 10px 0;">
        <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
          <span style="font-size: 18px;">🎉</span>
          <span style="font-size: 16px; font-weight: 500;">Operação Realizada!</span>
        </div>
        <div style="font-size: 14px; margin-top: 5px;">Tudo funcionou perfeitamente</div>
      </div>
      <div style="color: #666; margin-top: 15px; font-size: 14px;">
        ${message}
      </div>
    `,
    confirmButtonText: 'Continuar',
    confirmButtonColor: '#10b981'
  });
};

// 3. Alerta de erro (padrão vermelho)
export const showErrorAlert = (title = '❌ Erro!', message = 'Ocorreu um erro inesperado. Tente novamente.') => {
  return Swal.fire({
    ...defaultConfig,
    title,
    html: `
      <div style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white; padding: 15px; border-radius: 10px; margin: 10px 0;">
        <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
          <span style="font-size: 18px;">⚠️</span>
          <span style="font-size: 16px; font-weight: 500;">Ops!</span>
        </div>
        <div style="font-size: 14px; margin-top: 5px;">Algo deu errado</div>
      </div>
      <div style="color: #666; margin-top: 15px; font-size: 14px;">
        ${message}
      </div>
    `,
    confirmButtonText: 'Tentar Novamente',
    confirmButtonColor: '#ef4444'
  });
};

// 4. Modal de formulário de teste (padrão azul)
export const showTestFormModal = () => {
  return Swal.fire({
    ...defaultConfig,
    title: '📺 Teste Grátis - SmartV',
    html: `
      <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px; border-radius: 10px; margin: 10px 0;">
        <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
          <span style="font-size: 18px;">🎯</span>
          <span style="font-size: 16px; font-weight: 500;">Teste Grátis por 4 Horas</span>
        </div>
        <div style="font-size: 14px; margin-top: 5px;">Experimente nossos canais gratuitamente</div>
      </div>
      
      <div style="text-align: left; margin-top: 20px;">
        <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">Nome Completo</label>
        <input id="swal-input1" class="swal2-input" placeholder="Seu nome completo" style="margin: 0 0 15px 0;">
        
        <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">E-mail</label>
        <input id="swal-input2" class="swal2-input" type="email" placeholder="<EMAIL>" style="margin: 0;">
      </div>
    `,
    showCancelButton: true,
    confirmButtonText: 'Liberar Teste Grátis',
    cancelButtonText: 'Cancelar',
    confirmButtonColor: '#667eea',
    cancelButtonColor: '#6b7280',
    preConfirm: () => {
      const name = document.getElementById('swal-input1').value;
      const email = document.getElementById('swal-input2').value;
      
      if (!name || !email) {
        Swal.showValidationMessage('Por favor, preencha todos os campos');
        return false;
      }
      
      if (!email.includes('@')) {
        Swal.showValidationMessage('Por favor, insira um e-mail válido');
        return false;
      }
      
      return { name, email };
    }
  });
};

// 5. Modal de pagamento PIX (padrão amarelo/azul)
export const showPixPaymentModal = (value = 'R$ 29,90', plan = 'Plano Plano Infantil', qrCode = null, pixCode = null) => {
  return Swal.fire({
    ...defaultConfig,
    title: '💳 Pagamento PIX',
    html: `
      <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px; border-radius: 10px; margin: 10px 0;">
        <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
          <span style="font-size: 18px;">💰</span>
          <span style="font-size: 16px; font-weight: 500;">Valor: ${value}</span>
        </div>
        <div style="font-size: 14px; margin-top: 5px;">${plan}</div>
      </div>
      
      <div style="color: #666; margin: 15px 0; font-size: 14px;">
        Escaneie o QR Code ou copie o código PIX
      </div>
      
      <div style="background: #f3f4f6; border: 2px dashed #d1d5db; border-radius: 10px; padding: 30px; margin: 15px 0;">
        <div style="color: #9ca3af; font-size: 14px;">QR Code PIX</div>
        ${qrCode ? `<img src="${qrCode}" alt="QR Code PIX" style="max-width: 200px; margin-top: 10px;">` : ''}
      </div>
      
      <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px; border-radius: 10px; margin: 15px 0;">
        <div style="font-size: 16px; font-weight: 500; margin-bottom: 10px;">Como pagar:</div>
        <div style="text-align: left; font-size: 14px; line-height: 1.6;">
          1. Abra o app do seu banco<br>
          2. Escolha a opção PIX<br>
          3. Escaneie o QR Code ou cole o código<br>
          4. Confirme o pagamento
        </div>
      </div>
    `,
    showCancelButton: true,
    confirmButtonText: '📋 Copiar Código',
    cancelButtonText: '✅ Fechar',
    confirmButtonColor: '#667eea',
    cancelButtonColor: '#10b981'
  });
};

// 6. Modal de confirmação (padrão azul)
export const showConfirmationModal = (title = '❓ Confirmação', message = 'Deseja continuar com esta ação?') => {
  return Swal.fire({
    ...defaultConfig,
    title,
    html: `
      <div style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; padding: 15px; border-radius: 10px; margin: 10px 0;">
        <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
          <span style="font-size: 18px;">❓</span>
          <span style="font-size: 16px; font-weight: 500;">Confirmação Necessária</span>
        </div>
      </div>
      <div style="color: #666; margin-top: 15px; font-size: 14px;">
        ${message}
      </div>
    `,
    showCancelButton: true,
    confirmButtonText: 'Sim, Continuar',
    cancelButtonText: 'Cancelar',
    confirmButtonColor: '#3b82f6',
    cancelButtonColor: '#6b7280'
  });
};

// 7. Modal de informação (padrão ciano)
export const showInfoModal = (title = 'ℹ️ Informação', message = 'Informação importante') => {
  return Swal.fire({
    ...defaultConfig,
    title,
    html: `
      <div style="background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%); color: white; padding: 15px; border-radius: 10px; margin: 10px 0;">
        <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
          <span style="font-size: 18px;">ℹ️</span>
          <span style="font-size: 16px; font-weight: 500;">Informação</span>
        </div>
      </div>
      <div style="color: #666; margin-top: 15px; font-size: 14px;">
        ${message}
      </div>
    `,
    confirmButtonText: 'Entendi',
    confirmButtonColor: '#06b6d4'
  });
};

// 8. Modal de aviso (padrão laranja)
export const showWarningModal = (title = '⚠️ Aviso', message = 'Atenção para esta informação importante') => {
  return Swal.fire({
    ...defaultConfig,
    title,
    html: `
      <div style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; padding: 15px; border-radius: 10px; margin: 10px 0;">
        <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
          <span style="font-size: 18px;">⚠️</span>
          <span style="font-size: 16px; font-weight: 500;">Atenção!</span>
        </div>
      </div>
      <div style="color: #666; margin-top: 15px; font-size: 14px;">
        ${message}
      </div>
    `,
    confirmButtonText: 'Entendi',
    confirmButtonColor: '#f59e0b'
  });
};

// 9. Modal de resultado de teste (padrão verde com detalhes organizados)
export const showTestResultModal = (testData) => {
  // Função para organizar os dados em seções estruturadas
  const formatTestDataStructured = (data) => {
    if (!data) return 'Dados do teste serão exibidos aqui...';

    // Se já é uma string formatada, tentar extrair informações estruturadas
    if (typeof data === 'string') {
      return formatStringToStructured(data);
    }

    // Se é um objeto, formatar estruturadamente
    if (typeof data === 'object') {
      return formatObjectToStructured(data);
    }

    return data;
  };

  const formatStringToStructured = (text) => {
    let structured = '';

    // Primeiro, tentar detectar se é JSON e extrair dados
    let jsonData = null;
    try {
      jsonData = JSON.parse(text);
    } catch (e) {
      // Não é JSON, continuar com regex
    }

    // Se é JSON, extrair dados estruturados
    if (jsonData) {
      const username = jsonData.username || 'N/A';
      const password = jsonData.password || 'N/A';
      const dns = jsonData.dns || 'N/A';
      const packageName = jsonData.package || 'IPTV TESTE COMPLETO SEM ADULTOS';
      const expiresAt = jsonData.expiresAtFormatted || jsonData.expiresAt || 'N/A';
      const createdAt = jsonData.createdAtFormatted || jsonData.createdAt || 'N/A';
      const connections = jsonData.connections || 1;
      const payUrl = jsonData.payUrl || '';
      const price = jsonData.price || 'R$ 0,00';

      // Formato legível como no exemplo fornecido
      structured = `
<div style="background: #ffffff; padding: 20px; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; font-size: 14px; line-height: 1.8; color: #333;">
  <div style="margin-bottom: 8px;"><span style="color: #10b981;">✅</span> <strong>Usuário:</strong> ${username}</div>
  <div style="margin-bottom: 8px;"><span style="color: #10b981;">✅</span> <strong>Senha:</strong> ${password}</div>
  <div style="margin-bottom: 8px;"><span style="color: #f59e0b;">📦</span> <strong>Plano:</strong> ${packageName}</div>
  <div style="margin-bottom: 8px;"><span style="color: #8b5cf6;">💳</span> <strong>Assinar/Renovar Plano:</strong> <a href="${payUrl}" target="_blank" style="color: #3b82f6; text-decoration: none;">${payUrl}</a></div>
  <div style="margin-bottom: 8px;"><span style="color: #10b981;">💵</span> <strong>Preço do Plano:</strong> ${price}</div>
  <div style="margin-bottom: 8px;"><span style="color: #6b7280;">🗓️</span> <strong>Criado em:</strong> ${createdAt}</div>
  <div style="margin-bottom: 8px;"><span style="color: #6b7280;">🗓️</span> <strong>Vencimento:</strong> ${expiresAt}</div>
  <div style="margin-bottom: 16px;"><span style="color: #3b82f6;">📶</span> <strong>Conexões:</strong> ${connections}</div>

  <div style="margin-bottom: 8px;"><span style="color: #f97316;">🟠</span> <strong>DNS XCIPTV:</strong> ${dns}</div>
  <div style="margin-bottom: 8px;"><span style="color: #f97316;">🟠</span> <strong>DNS SMARTERS:</strong> ${dns}</div>
  <div style="margin-bottom: 16px; font-size: 12px; color: #6b7280;">Usem com 3 barras (SMARTERS)</div>`;

      // Continuar com os links no formato legível
      const m3uLink = `${dns}/get.php?username=${username}&password=${password}&type=m3u_plus&output=mpegts`;
      const m3uShortLink = dns.includes('bitbig.click') ? `http://e.bitbig.click/p/${username}/${password}/m3u` : `${dns}/p/${username}/${password}/m3u`;
      const hlsLink = `${dns}/get.php?username=${username}&password=${password}&type=m3u_plus&output=hls`;
      const hlsShortLink = dns.includes('bitbig.click') ? `http://e.bitbig.click/p/${username}/${password}/hls` : `${dns}/p/${username}/${password}/hls`;
      const ssiptvLink = dns.includes('bitbig.click') ? `http://e.bitbig.click/p/${username}/${password}/ssiptv` : `${dns}/p/${username}/${password}/ssiptv`;

      structured += `
  <div style="margin-bottom: 8px;"><span style="color: #10b981;">🟢</span> <strong>Link (M3U):</strong> ${m3uLink}</div>
  <div style="margin-bottom: 8px;"><span style="color: #10b981;">🟢</span> <strong>Link Curto (M3U):</strong> ${m3uShortLink}</div>
  <div style="margin-bottom: 8px;"><span style="color: #eab308;">🟡</span> <strong>Link (HLS):</strong> ${hlsLink}</div>
  <div style="margin-bottom: 8px;"><span style="color: #eab308;">🟡</span> <strong>Link Curto (HLS):</strong> ${hlsShortLink}</div>
  <div style="margin-bottom: 16px;"><span style="color: #ef4444;">🔴</span> <strong>Link (SSIPTV):</strong> ${ssiptvLink}</div>

  <div style="margin-bottom: 8px;"><span style="color: #3b82f6;">🔗</span><strong>Manual do IPTV</strong></div>
  <div style="margin-bottom: 16px;"><span style="color: #10b981;">🟢</span><a href="https://www.manualiptv.com/" target="_blank" style="color: #3b82f6; text-decoration: none;">https://www.manualiptv.com/</a></div>

  <div style="margin-bottom: 8px;"><span style="color: #6b7280;">📺</span> <strong>DNS STB / SmartUp:</strong> XXXX</div>
  <div style="margin-bottom: 16px;"><span style="color: #6b7280;">📺</span> <strong>WebPlayer:</strong> http://XXXXXX/</div>

  <div style="margin-bottom: 8px;"><span style="color: #10b981;">✅</span> <strong>PARA ANDROID:</strong></div>

  <div style="margin-bottom: 8px;"><span style="color: #10b981;">🟢</span><strong>Aplicativo 3 em 1</strong></div>
  <div style="margin-bottom: 8px;"><span style="color: #10b981;">🟢</span><a href="https://aftv.news/217356" target="_blank" style="color: #3b82f6; text-decoration: none;">https://aftv.news/217356</a></div>
  <div style="margin-bottom: 12px;"><span style="color: #10b981;">🟢</span><strong>Código Downloader:</strong> 217356</div>

  <div style="margin-bottom: 8px;"><span style="color: #eab308;">🟡</span><strong>Aplicativo Thunder</strong></div>
  <div style="margin-bottom: 8px;"><span style="color: #eab308;">🟡</span><a href="https://aftv.news/515800" target="_blank" style="color: #3b82f6; text-decoration: none;">https://aftv.news/515800</a></div>
  <div style="margin-bottom: 12px;"><span style="color: #eab308;">🟡</span><strong>Código Downloader:</strong> 515800</div>

  <div style="margin-bottom: 8px;"><span style="color: #ef4444;">🔴</span><strong>Aplicativo YouTV</strong></div>
  <div style="margin-bottom: 8px;"><span style="color: #ef4444;">🔴</span><a href="https://aftv.news/954983" target="_blank" style="color: #3b82f6; text-decoration: none;">https://aftv.news/954983</a></div>
  <div style="margin-bottom: 8px;"><span style="color: #ef4444;">🔴</span><strong>Código Downloader:</strong> 954983</div>
</div>`;

      return structured;
    }

    // Fallback: processar como texto normal - usar formato legível também
    // Extrair informações principais
    const usernameMatch = text.match(/(?:Usuário|Username)[:\s]*([^\n\r*]+)/i);
    const passwordMatch = text.match(/(?:Senha|Password)[:\s]*([^\n\r*]+)/i);
    const expiresMatch = text.match(/(?:Vencimento|Expira em)[:\s]*([^\n\r*]+)/i);
    const planMatch = text.match(/(?:Plano)[:\s]*([^\n\r*]+)/i);
    const dnsMatch = text.match(/(?:DNS)[:\s]*([^\n\r*]+)/i);

    // Se encontrou dados básicos, usar formato legível
    if (usernameMatch && passwordMatch) {
      const username = usernameMatch[1].trim().replace(/\*/g, '');
      const password = passwordMatch[1].trim().replace(/\*/g, '');
      const dns = dnsMatch ? dnsMatch[1].trim().replace(/\*/g, '') : 'N/A';
      const packageName = planMatch ? planMatch[1].trim().replace(/\*/g, '') : 'IPTV TESTE COMPLETO SEM ADULTOS';
      const expiresAt = expiresMatch ? expiresMatch[1].trim().replace(/\*/g, '') : 'N/A';

      structured = `
<div style="background: #ffffff; padding: 20px; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; font-size: 14px; line-height: 1.8; color: #333;">
  <div style="margin-bottom: 8px;"><span style="color: #10b981;">✅</span> <strong>Usuário:</strong> ${username}</div>
  <div style="margin-bottom: 8px;"><span style="color: #10b981;">✅</span> <strong>Senha:</strong> ${password}</div>
  <div style="margin-bottom: 8px;"><span style="color: #f59e0b;">📦</span> <strong>Plano:</strong> ${packageName}</div>
  <div style="margin-bottom: 8px;"><span style="color: #6b7280;">🗓️</span> <strong>Vencimento:</strong> ${expiresAt}</div>
  <div style="margin-bottom: 16px;"><span style="color: #3b82f6;">📶</span> <strong>Conexões:</strong> 1</div>

  <div style="margin-bottom: 8px;"><span style="color: #f97316;">🟠</span> <strong>DNS XCIPTV:</strong> ${dns}</div>
  <div style="margin-bottom: 8px;"><span style="color: #f97316;">🟠</span> <strong>DNS SMARTERS:</strong> ${dns}</div>
  <div style="margin-bottom: 16px; font-size: 12px; color: #6b7280;">Usem com 3 barras (SMARTERS)</div>
</div>`;
    } else {
      // Se não conseguiu extrair dados básicos, mostrar texto original formatado
      structured = `
<div style="background: #ffffff; padding: 20px; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; font-size: 14px; line-height: 1.8; color: #333; white-space: pre-wrap;">
${text}
</div>`;
    }

    return structured;
  };

  const formatObjectToStructured = (obj) => {
    // Implementar formatação para objetos estruturados se necessário
    return JSON.stringify(obj, null, 2);
  };

  return Swal.fire({
    ...defaultConfig,
    title: '🎯 Teste Gerado com Sucesso!',
    html: `
      <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 15px; border-radius: 10px; margin: 10px 0;">
        <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
          <span style="font-size: 18px;">✅</span>
          <span style="font-size: 16px; font-weight: 500;">Teste Liberado!</span>
        </div>
        <div style="font-size: 14px; margin-top: 5px;">Seus dados de acesso estão organizados abaixo</div>
      </div>

      <div style="
        background: #ffffff;
        border-radius: 10px;
        padding: 16px;
        margin: 15px 0;
        text-align: left;
        max-height: 450px;
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: #cbd5e1 #f1f5f9;
        border: 1px solid #e2e8f0;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      " class="test-result-content">
        ${formatTestDataStructured(testData)}
      </div>

      <div style="color: #666; font-size: 12px; margin-top: 10px; text-align: center;">
        💡 Salve essas informações para acessar seus canais
      </div>
    `,
    confirmButtonText: '📋 Copiar Dados',
    showCancelButton: true,
    cancelButtonText: '✅ Entendi',
    confirmButtonColor: '#10b981',
    cancelButtonColor: '#6b7280',
    width: '750px',
    customClass: {
      ...defaultConfig.customClass,
      popup: 'swal-clean-theme test-result-modal'
    },
    didOpen: () => {
      // Adicionar estilos CSS para melhorar a aparência
      const style = document.createElement('style');
      style.textContent = `
        .test-result-content::-webkit-scrollbar {
          width: 6px;
        }
        .test-result-content::-webkit-scrollbar-track {
          background: #f1f5f9;
          border-radius: 3px;
        }
        .test-result-content::-webkit-scrollbar-thumb {
          background: #cbd5e1;
          border-radius: 3px;
        }
        .test-result-content::-webkit-scrollbar-thumb:hover {
          background: #94a3b8;
        }
        .test-result-modal .test-result-content h4 {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .test-result-modal .test-result-content code {
          font-family: 'Courier New', Consolas, Monaco, monospace;
        }
      `;
      document.head.appendChild(style);
    },
    preConfirm: () => {
      // Extrair texto limpo para cópia
      const extractCleanText = (data) => {
        if (typeof data === 'string') {
          // Extrair informações principais do texto
          const usernameMatch = data.match(/(?:Usuário|Username)[:\s]*([^\n\r*]+)/i);
          const passwordMatch = data.match(/(?:Senha|Password)[:\s]*([^\n\r*]+)/i);
          const expiresMatch = data.match(/(?:Vencimento|Expira em)[:\s]*([^\n\r*]+)/i);
          const planMatch = data.match(/(?:Plano)[:\s]*([^\n\r*]+)/i);
          const dnsMatch = data.match(/(?:DNS)[:\s]*([^\n\r*]+)/i);
          const m3uMatch = data.match(/(?:LINK M3U)[:\s]*(http[s]?:\/\/[^\s\n*]+)/i);
          const hlsMatch = data.match(/(?:LINK HLS)[:\s]*(http[s]?:\/\/[^\s\n*]+)/i);
          const ssiptvMatch = data.match(/(?:SSIPTV)[:\s]*(http[s]?:\/\/[^\s\n*]+)/i);

          let cleanText = '🎯 DADOS DO TESTE IPTV\n\n';

          if (usernameMatch || passwordMatch) {
            cleanText += '👤 CREDENCIAIS:\n';
            if (usernameMatch) cleanText += `📧 Usuário: ${usernameMatch[1].trim().replace(/\*/g, '')}\n`;
            if (passwordMatch) cleanText += `🔐 Senha: ${passwordMatch[1].trim().replace(/\*/g, '')}\n`;
            if (planMatch) cleanText += `📦 Plano: ${planMatch[1].trim().replace(/\*/g, '')}\n`;
            if (expiresMatch) cleanText += `⏰ Válido até: ${expiresMatch[1].trim().replace(/\*/g, '')}\n`;
            cleanText += '\n';
          }

          if (dnsMatch) {
            cleanText += '📡 SERVIDOR:\n';
            cleanText += `${dnsMatch[1].trim().replace(/\*/g, '')}\n\n`;
          }

          if (m3uMatch || hlsMatch || ssiptvMatch) {
            cleanText += '🔗 LINKS DE ACESSO:\n';
            if (m3uMatch) cleanText += `🟢 M3U: ${m3uMatch[1].replace(/\*/g, '')}\n`;
            if (hlsMatch) cleanText += `🟡 HLS: ${hlsMatch[1].replace(/\*/g, '')}\n`;
            if (ssiptvMatch) cleanText += `📱 SSIPTV: ${ssiptvMatch[1].replace(/\*/g, '')}\n`;
            cleanText += '\n';
          }

          cleanText += '📱 COMO USAR:\n';
          cleanText += '1. Baixe um app IPTV (IPTV Smarters, TiviMate, SS IPTV)\n';
          cleanText += '2. Use as credenciais acima para fazer login\n';
          cleanText += '3. Configure o servidor DNS se necessário\n';
          cleanText += '4. Aproveite seus canais!\n';

          return cleanText;
        }
        return data;
      };

      const textToCopy = extractCleanText(testData);

      // Copiar dados para clipboard
      if (navigator.clipboard) {
        navigator.clipboard.writeText(textToCopy).then(() => {
          Swal.fire({
            title: 'Dados copiados!',
            text: 'As informações foram copiadas para sua área de transferência',
            icon: 'success',
            timer: 2000,
            showConfirmButton: false
          });
        }).catch(() => {
          // Fallback para navegadores mais antigos
          const textArea = document.createElement('textarea');
          textArea.value = textToCopy;
          document.body.appendChild(textArea);
          textArea.select();
          document.execCommand('copy');
          document.body.removeChild(textArea);

          Swal.fire({
            title: 'Dados copiados!',
            text: 'As informações foram copiadas para sua área de transferência',
            icon: 'success',
            timer: 2000,
            showConfirmButton: false
          });
        });
      } else {
        // Fallback para navegadores mais antigos
        const textArea = document.createElement('textarea');
        textArea.value = textToCopy;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);

        Swal.fire({
          title: 'Dados copiados!',
          text: 'As informações foram copiadas para sua área de transferência',
          icon: 'success',
          timer: 2000,
          showConfirmButton: false
        });
      }

      return false; // Não fechar o modal automaticamente
    }
  });
};

// 10. Modal de pagamento confirmado (padrão verde com detalhes)
export const showPaymentConfirmedAlert = (paymentData) => {
  const { customerName, planType, amount, paymentId, paidAt } = paymentData;
  const formattedDate = new Date(paidAt).toLocaleString('pt-BR');
  const formattedAmount = `R$ ${amount.toFixed(2).replace('.', ',')}`;

  return Swal.fire({
    ...defaultConfig,
    title: '🎉 Pagamento Confirmado!',
    html: `
      <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 15px; border-radius: 10px; margin: 10px 0;">
        <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
          <span style="font-size: 18px;">✅</span>
          <span style="font-size: 16px; font-weight: 500;">Pagamento Aprovado!</span>
        </div>
        <div style="font-size: 14px; margin-top: 5px;">Seu acesso foi liberado com sucesso</div>
      </div>

      <div style="background: #f8fafc; border-radius: 10px; padding: 15px; margin: 15px 0; text-align: left;">
        <div style="font-size: 14px; color: #374151;">
          <div style="margin-bottom: 8px;"><strong>👤 Cliente:</strong> ${customerName}</div>
          <div style="margin-bottom: 8px;"><strong>📺 Plano:</strong> ${planType}</div>
          <div style="margin-bottom: 8px;"><strong>💰 Valor:</strong> ${formattedAmount}</div>
          <div style="margin-bottom: 8px;"><strong>📅 Data:</strong> ${formattedDate}</div>
          <div style="margin-bottom: 8px;"><strong>🔢 ID:</strong> ${paymentId}</div>
        </div>
      </div>

      <div style="color: #666; font-size: 12px; margin-top: 10px;">
        🎯 Seu acesso aos canais foi liberado automaticamente!
      </div>
    `,
    confirmButtonText: '🚀 Acessar Canais',
    confirmButtonColor: '#10b981',
    width: '500px',
    customClass: {
      ...defaultConfig.customClass,
      popup: 'swal-clean-theme payment-confirmed-modal'
    }
  });
};
