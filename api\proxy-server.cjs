const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const cors = require('cors');
const QRCode = require('qrcode');

const app = express();
const PORT = 3001;

// Configurar CORS manualmente para máxima compatibilidade
app.use((req, res, next) => {
  const origin = req.headers.origin;

  // Permitir origens específicas ou localhost
  if (origin && (
    origin.includes('localhost') ||
    origin.includes('127.0.0.1') ||
    origin.includes('***********') ||
    origin.includes('smartv.shop')
  )) {
    res.header('Access-Control-Allow-Origin', origin);
  } else {
    res.header('Access-Control-Allow-Origin', '*');
  }

  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Origin');
  res.header('Access-Control-Allow-Credentials', 'true');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Middleware para logs
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url} from ${req.ip}`);
  next();
});

// Middleware para parsing JSON
app.use(express.json());

// Rota de teste IPTV
app.post('/test/request', async (req, res) => {
  console.log('📺 Gerando teste IPTV:', req.body);

  const { name, email, plan } = req.body;

  try {
    // Fazer requisição para a API real
    const apiResponse = await fetch('https://pop.sigma.vin/api/chatbot/e6WnZE7WK8/rlKWO3Wzo7', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'SmartV-TestBot/1.0',
      },
      body: JSON.stringify({
        name: name,
        email: email,
        plan: plan || 'Básico'
      })
    });

    if (!apiResponse.ok) {
      throw new Error(`API Error: ${apiResponse.status}`);
    }

    const responseText = await apiResponse.text();
    console.log('✅ Resposta RAW da API real:', responseText);

    // Tentar parsear como JSON primeiro
    let apiData;
    try {
      apiData = JSON.parse(responseText);
      console.log('✅ Resposta JSON da API real:', apiData);
    } catch (jsonError) {
      console.log('⚠️ Resposta não é JSON válido, tratando como texto');
      apiData = { rawResponse: responseText };
    }

    // Processar e formatar a resposta para incluir todos os dados
    const processedResponse = processApiResponse(apiData, responseText);
    console.log('✅ Resposta processada:', processedResponse);

    // Retornar a resposta processada
    res.json(processedResponse);

  } catch (error) {
    console.error('❌ Erro na API real:', error);

    // Fallback: gerar teste local se a API falhar
    const user = Math.floor(Math.random() * 90000000) + 10000000;
    const password = Math.floor(Math.random() * 90000000) + 10000000;

    // Data de expiração (24 horas)
    const expirationDate = new Date();
    expirationDate.setHours(expirationDate.getHours() + 24);

  // Gerar resposta completa formatada como no padrão CINE FLIX
  const createdDate = new Date().toLocaleString('pt-BR');
  const expiresDate = expirationDate.toLocaleString('pt-BR');

  const fullFormattedResponse = `🎬 *CINE FLIX PRO* 🎬

✅ *Usuário*: ${user}
✅ *Senha*: ${password}

📌 *CODE*: ${user}

📺 *DNS STB/SmartUpV3*: cdn.dns7b.site

🟠 *URL XCIPTV*: http://cdn.dns7b.site/get.php?username=${user}&password=${password}&type=m3u_plus&output=mpegts

🟢 *Link (M3U)*: http://cdn.dns7b.site/get.php?username=${user}&password=${password}&type=m3u_plus&output=mpegts
🟢 *Link Curto (M3U)*: http://e.cdn.dns7b.site/p/${user}/${password}/m3u

🟡 *Link (HLS)*: http://cdn.dns7b.site/get.php?username=${user}&password=${password}&type=m3u_plus&output=hls
🟡 *Link Curto (HLS)*: http://e.cdn.dns7b.site/p/${user}/${password}/hls

🔴 *Link (SSIPTV)*: http://e.cdn.dns7b.site/p/${user}/${password}/ssiptv

📺 *WEB PLAYER*: http://webtv.iptvblinkplayer.com/xtream-code

📺 *IPTV STREAM*: http://cdn.dns7b.site/live/${user}/${password}

🗓️ *Vencimento*: ${expiresDate}
📶 *Conexões*: 1
📦 *Plano*: 👽TESTE IPTV S/ ADULTOS 👽 PRO
💵 *Preço do Plano*: R$ 0,00
🗓️ *Criado em*: ${createdDate}
💳 *Assinar/Renovar Plano*: https://cineflx.topbrr.shop/#/checkout/RXDgQzY1ex/MeWew2A8Ln`;

  // Formato compatível com a interface TestResponse
  const testData = {
    success: true,
    message: "Teste liberado com sucesso! Acesso válido por 24 horas.",
    testUrl: "http://cdn.dns7b.site",
    credentials: {
      username: user.toString(),
      password: password.toString()
    },
    accessDetails: {
      code: user.toString(),
      dnsStb: "cdn.dns7b.site",
      urlXciptv: [`http://cdn.dns7b.site/get.php?username=${user}&password=${password}&type=m3u_plus&output=mpegts`],
      linkM3u: `http://cdn.dns7b.site/get.php?username=${user}&password=${password}&type=m3u_plus&output=mpegts`,
      linkM3uShort: `http://e.cdn.dns7b.site/p/${user}/${password}/m3u`,
      linkHls: `http://cdn.dns7b.site/get.php?username=${user}&password=${password}&type=m3u_plus&output=hls`,
      linkHlsShort: `http://e.cdn.dns7b.site/p/${user}/${password}/hls`,
      linkSsiptv: `http://e.cdn.dns7b.site/p/${user}/${password}/ssiptv`,
      webPlayers: [`http://webtv.iptvblinkplayer.com/xtream-code`],
      iptvStream: `http://cdn.dns7b.site/live/${user}/${password}`,
      expiresAt: expiresDate,
      connections: 1,
      planName: "👽TESTE IPTV S/ ADULTOS 👽 PRO",
      price: "R$ 0,00",
      createdAt: createdDate,
      renewalUrl: "https://cineflx.topbrr.shop/#/checkout/RXDgQzY1ex/MeWew2A8Ln"
    },
    rawResponse: fullFormattedResponse,
    // Manter compatibilidade com o formato antigo
    user: user.toString(),
    password: password.toString(),
    expirationDate: expiresDate,
    urls: {
      m3u: `http://cdn.dns7b.site/get.php?username=${user}&password=${password}&type=m3u_plus&output=mpegts`,
      hls: `http://cdn.dns7b.site/get.php?username=${user}&password=${password}&type=m3u_plus&output=hls`,
      short_m3u: `http://e.cdn.dns7b.site/p/${user}/${password}/m3u`,
      short_hls: `http://e.cdn.dns7b.site/p/${user}/${password}/hls`,
      ssiptv: `http://e.cdn.dns7b.site/p/${user}/${password}/ssiptv`
    },
    server: {
      host: 'cdn.dns7b.site',
      port: '80'
    }
  };

    console.log('✅ Teste gerado (fallback):', testData);
    res.json(testData);
  }
});

// Rota de pagamento PIX - INTEGRAÇÃO REAL MERCADO PAGO
app.post('/payment/pix', async (req, res) => {
  console.log('💰 Gerando PIX REAL (Mercado Pago):', req.body);

  const { amount, description, customerName, customerEmail, customerDocument, planType } = req.body;

  // Token de acesso do Mercado Pago (TESTE)
  const accessToken = 'TEST-3497649211683628-100816-c579d6f751942537a6d13fddbefd9a61-1022896072';

  try {
    // Validar documento (CPF/CNPJ)
    const document = (customerDocument || '').replace(/\D/g, '');
    if (!document || (document.length !== 11 && document.length !== 14)) {
      return res.status(400).json({ error: 'Documento inválido. Informe um CPF ou CNPJ válido.' });
    }

    // Criar pagamento PIX usando API real do Mercado Pago
    const paymentData = {
      transaction_amount: parseFloat(amount),
      description: description || `SmartV IPTV - Plano ${planType}`,
      payment_method_id: 'pix',
      payer: {
        email: customerEmail || '<EMAIL>',
        first_name: (customerName || 'Cliente').split(' ')[0],
        last_name: (customerName || 'SmartV').split(' ').slice(1).join(' ') || 'SmartV',
        identification: {
          type: document.length === 11 ? 'CPF' : 'CNPJ',
          number: document
        }
      },
      external_reference: `SMARTV_${planType}_${Date.now()}`,
      notification_url: 'https://smartv.shop/webhook/mercadopago',
      date_of_expiration: new Date(Date.now() + 15 * 60 * 1000).toISOString()
    };

    console.log('📤 Enviando para Mercado Pago:', JSON.stringify(paymentData, null, 2));

    const response = await fetch('https://api.mercadopago.com/v1/payments', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
        'X-Idempotency-Key': `SMARTV_${planType}_${Date.now()}`
      },
      body: JSON.stringify(paymentData)
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('❌ Erro Mercado Pago:', response.status, errorData);
      throw new Error(`Mercado Pago API error: ${response.status} - ${errorData}`);
    }

    const paymentResponse = await response.json();
    console.log('✅ Resposta Mercado Pago:', JSON.stringify(paymentResponse, null, 2));

    if (!paymentResponse.id) {
      throw new Error('Falha ao criar pagamento no Mercado Pago');
    }

    // Extrair informações do PIX
    const pixInfo = paymentResponse.point_of_interaction?.transaction_data;

    if (!pixInfo?.qr_code || !pixInfo?.qr_code_base64) {
      throw new Error('QR Code PIX não foi gerado pelo Mercado Pago');
    }

    // Resposta com dados REAIS do Mercado Pago
    const pixData = {
      id: paymentResponse.id.toString(),
      status: paymentResponse.status || 'pending',
      qrCode: pixInfo.qr_code,
      qrCodeUrl: `data:image/png;base64,${pixInfo.qr_code_base64}`,
      pixKey: pixInfo.qr_code,
      amount: parseFloat(amount),
      transactionAmount: paymentResponse.transaction_amount || parseFloat(amount),
      description: paymentResponse.description || `SmartV IPTV - Plano ${planType}`,
      expiresAt: paymentResponse.date_of_expiration || new Date(Date.now() + 15 * 60 * 1000).toISOString(),
      mercadoPagoId: paymentResponse.id,
      mercadoPagoStatus: paymentResponse.status
    };

    console.log('✅ PIX REAL gerado com sucesso:', pixData.id);
    res.json(pixData);

  } catch (error) {
    console.error('❌ Erro ao gerar PIX real:', error);

    // Fallback: PIX simulado se a API do Mercado Pago falhar
    console.log('⚠️ Usando PIX simulado como fallback...');

    const pixCode = `00020126580014br.gov.bcb.pix0136${Math.random().toString(36).substr(2, 32)}520400005303986540${parseFloat(amount).toFixed(2)}5802BR5925${customerName || 'SmartV IPTV'}6009SAO PAULO62070503***6304${Math.random().toString(36).substr(2, 4).toUpperCase()}`;

    let qrCodeBase64;
    try {
      qrCodeBase64 = await QRCode.toDataURL(pixCode, {
        width: 256,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });
    } catch (qrError) {
      console.error('Erro ao gerar QR Code:', qrError);
      qrCodeBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    }

    const fallbackPixData = {
      id: Math.random().toString(36).substr(2, 9),
      status: 'pending',
      qrCode: pixCode,
      qrCodeUrl: qrCodeBase64,
      pixKey: pixCode,
      amount: parseFloat(amount),
      transactionAmount: parseFloat(amount),
      description: description || `SmartV IPTV - ${planType}`,
      expiresAt: new Date(Date.now() + 15 * 60 * 1000).toISOString(),
      fallback: true,
      error: error.message
    };

    console.log('✅ PIX simulado gerado (fallback):', fallbackPixData.id);
    res.json(fallbackPixData);
  }
});

// Rota de health check
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Proxy para o Encore backend (fallback)
app.use('/', createProxyMiddleware({
  target: 'http://localhost:4000',
  changeOrigin: true,
  logLevel: 'debug',
  onError: (err, req, res) => {
    console.error('Proxy error:', err);
    res.status(500).json({ error: 'Backend não disponível' });
  },
  onProxyReq: (proxyReq, req, res) => {
    console.log('Proxying request to:', proxyReq.path);
  }
}));

// Função para processar a resposta da API real
function processApiResponse(apiData, rawText) {
  try {
    // Se a resposta tem username e password diretamente (formato JSON)
    if (apiData.username && apiData.password) {
      console.log('📋 Processando resposta JSON com credenciais diretas');

      return {
        success: true,
        message: "Teste liberado com sucesso! Acesso válido por 4 horas.",
        testUrl: apiData.dns || "https://pop.sigma.vin/api/chatbot/e6WnZE7WK8/rlKWO3Wzo7",
        credentials: {
          username: apiData.username,
          password: apiData.password
        },
        accessDetails: {
          code: extractFromText(apiData.reply || rawText, /📌\s*\*?CODE\s*\*?\s*:\s*(\d+)/i),
          dnsStb: apiData.dns ? extractDnsFromUrl(apiData.dns) : extractFromText(apiData.reply || rawText, /🟠\s*\*?DNS\s*STB\*?\s*:\s*([^\n\r*]+)/i),
          urlXciptv: extractXciptvUrls(apiData.reply || rawText),
          linkM3u: extractFromText(apiData.reply || rawText, /🟢\s*\*?Link\s*M3U\*?\s*:\s*(http[s]?:\/\/[^\s\n*]+)/i),
          linkM3uShort: extractFromText(apiData.reply || rawText, /🟢\s*\*?Link\s*M3U\s*CURTO\*?\s*:\s*(http[s]?:\/\/[^\s\n*]+)/i),
          linkHls: extractFromText(apiData.reply || rawText, /🟡\s*\*?Link\s*HLS\*?\s*:\s*(http[s]?:\/\/[^\s\n*]+)/i),
          linkHlsShort: extractFromText(apiData.reply || rawText, /🟡\s*\*?Link\s*HLS\s*CURTO\*?\s*:\s*(http[s]?:\/\/[^\s\n*]+)/i),
          linkSsiptv: extractFromText(apiData.reply || rawText, /🔴\s*\*?Link\s*\(SSIPTV\)\*?\s*:\s*(http[s]?:\/\/[^\s\n*]+)/i),
          webPlayers: extractWebPlayers(apiData.reply || rawText),
          iptvStream: extractFromText(apiData.reply || rawText, /📺\s*\*?IPTV\s*STREAM\*?\s*:\s*(http[s]?:\/\/[^\s\n*]+)/i),
          expiresAt: apiData.expiresAtFormatted || apiData.expiresAt || extractFromText(apiData.reply || rawText, /🗓️\s*\*?Vencimento\*?\s*:\s*([^\n\r*]+)/i),
          connections: apiData.connections || extractFromText(apiData.reply || rawText, /📶\s*\*?Conexões\*?\s*:\s*(\d+)/i),
          planName: apiData.package || extractFromText(apiData.reply || rawText, /📦\s*\*?Plano\*?\s*:\s*([^\n\r*]+)/i),
          price: extractFromText(apiData.reply || rawText, /💵\s*\*?Preço\s*do\s*Plano\*?\s*:\s*([^\n\r*]+)/i),
          createdAt: apiData.createdAtFormatted || apiData.createdAt || extractFromText(apiData.reply || rawText, /🗓️\s*\*?Criado\s*em\*?\s*:\s*([^\n\r*]+)/i),
          renewalUrl: apiData.payUrl || extractFromText(apiData.reply || rawText, /💳\s*\*?Assinar\/Renovar\s*Plano\*?\s*:\s*(http[s]?:\/\/[^\s\n*]+)/i)
        },
        rawResponse: apiData.reply || rawText,
        // Manter compatibilidade com formato antigo
        user: apiData.username,
        password: apiData.password,
        dns: apiData.dns,
        package: apiData.package,
        expirationDate: apiData.expiresAtFormatted || apiData.expiresAt,
        payUrl: apiData.payUrl
      };
    }

    // Se a resposta tem um campo 'reply' ou 'data' (formato texto)
    if (apiData.reply || (apiData.data && apiData.data[0]?.message)) {
      const textContent = apiData.reply || apiData.data[0].message;
      console.log('📋 Processando resposta com campo reply/message');

      return parseTextResponse(textContent, rawText);
    }

    // Se é apenas texto puro
    console.log('📋 Processando resposta como texto puro');
    return parseTextResponse(rawText, rawText);

  } catch (error) {
    console.error('❌ Erro ao processar resposta da API:', error);
    return generateFallbackResponse();
  }
}

// Função para extrair dados de texto usando regex
function extractFromText(text, regex) {
  if (!text) return null;
  const match = text.match(regex);
  return match ? match[1].trim().replace(/\*/g, '') : null;
}

// Função para extrair DNS de URL
function extractDnsFromUrl(url) {
  if (!url) return null;
  const match = url.match(/https?:\/\/([^:\/\s]+)/);
  return match ? match[1] : null;
}

// Função para extrair URLs XCIPTV
function extractXciptvUrls(text) {
  if (!text) return [];
  const matches = text.match(/🟠\s*\*?URL\s*XCIPTV\*?:\s*(http[s]?:\/\/[^\s\n*]+)/gi);
  return matches ? matches.map(match => {
    const urlMatch = match.match(/(http[s]?:\/\/[^\s\n*]+)/i);
    return urlMatch ? urlMatch[1].replace(/\*/g, '') : '';
  }).filter(url => url) : [];
}

// Função para extrair web players
function extractWebPlayers(text) {
  if (!text) return [];
  const webPlayerSection = text.match(/📺\s*\*?WEB\s*PLAYER\*?:\s*((?:http[s]?:\/\/[^\s\n*]+\s*)+)/i);
  return webPlayerSection ?
    webPlayerSection[1].trim().split(/\s+/).filter(url => url.startsWith('http')).map(url => url.replace(/\*/g, '')) : [];
}

// Função para parsear resposta de texto
function parseTextResponse(text, rawText) {
  const username = extractFromText(text, /✅\s*\*?Usuário\*?:\s*(\d+)/i);
  const password = extractFromText(text, /✅\s*\*?Senha\*?:\s*(\d+)/i);

  if (!username || !password) {
    console.log('❌ Não foi possível extrair usuário/senha da resposta');
    return generateFallbackResponse();
  }

  return {
    success: true,
    message: "Teste liberado com sucesso! Acesso válido por 4 horas.",
    testUrl: "https://pop.sigma.vin/api/chatbot/e6WnZE7WK8/rlKWO3Wzo7",
    credentials: {
      username: username,
      password: password
    },
    accessDetails: {
      code: extractFromText(text, /📌\s*\*?CODE\s*\*?\s*:\s*(\d+)/i),
      dnsStb: extractFromText(text, /📺\s*\*?DNS\s*STB[^:]*:\s*([^\n\r*]+)/i),
      urlXciptv: extractXciptvUrls(text),
      linkM3u: extractFromText(text, /🟢\s*\*?Link[^:]*M3U[^:]*:\s*(http[s]?:\/\/[^\s\n*]+)/i),
      linkM3uShort: extractFromText(text, /🟢\s*\*?Link[^:]*M3U\s*CURTO[^:]*:\s*(http[s]?:\/\/[^\s\n*]+)/i),
      linkHls: extractFromText(text, /🟡\s*\*?Link[^:]*HLS[^:]*:\s*(http[s]?:\/\/[^\s\n*]+)/i),
      linkHlsShort: extractFromText(text, /🟡\s*\*?Link[^:]*HLS\s*CURTO[^:]*:\s*(http[s]?:\/\/[^\s\n*]+)/i),
      linkSsiptv: extractFromText(text, /🔴\s*\*?Link[^:]*SSIPTV[^:]*:\s*(http[s]?:\/\/[^\s\n*]+)/i),
      webPlayers: extractWebPlayers(text),
      iptvStream: extractFromText(text, /📺\s*\*?IPTV\s*STREAM\*?\s*:\s*(http[s]?:\/\/[^\s\n*]+)/i),
      expiresAt: extractFromText(text, /🗓️\s*\*?Vencimento\*?\s*:\s*([^\n\r*]+)/i),
      connections: extractFromText(text, /📶\s*\*?Conexões\*?\s*:\s*(\d+)/i),
      planName: extractFromText(text, /📦\s*\*?Plano\*?\s*:\s*([^\n\r*]+)/i),
      price: extractFromText(text, /💵\s*\*?Preço\s*do\s*Plano\*?\s*:\s*([^\n\r*]+)/i),
      createdAt: extractFromText(text, /🗓️\s*\*?Criado\s*em\*?\s*:\s*([^\n\r*]+)/i),
      renewalUrl: extractFromText(text, /💳\s*\*?Assinar\/Renovar\s*Plano\*?\s*:\s*(http[s]?:\/\/[^\s\n*]+)/i)
    },
    rawResponse: rawText,
    // Manter compatibilidade
    user: username,
    password: password,
    dns: extractFromText(text, /📺\s*\*?DNS\s*STB[^:]*:\s*([^\n\r*]+)/i),
    package: extractFromText(text, /📦\s*\*?Plano\*?\s*:\s*([^\n\r*]+)/i),
    expirationDate: extractFromText(text, /🗓️\s*\*?Vencimento\*?\s*:\s*([^\n\r*]+)/i),
    payUrl: extractFromText(text, /💳\s*\*?Assinar\/Renovar\s*Plano\*?\s*:\s*(http[s]?:\/\/[^\s\n*]+)/i)
  };
}

// Função para gerar resposta de fallback
function generateFallbackResponse() {
  const user = Math.floor(Math.random() * 90000000) + 10000000;
  const password = Math.floor(Math.random() * 90000000) + 10000000;
  const expirationDate = new Date();
  expirationDate.setHours(expirationDate.getHours() + 24);
  const createdDate = new Date().toLocaleString('pt-BR');
  const expiresDate = expirationDate.toLocaleString('pt-BR');

  const fullFormattedResponse = `🎬 *CINE FLIX PRO* 🎬

✅ *Usuário*: ${user}
✅ *Senha*: ${password}

📌 *CODE*: ${user}

📺 *DNS STB/SmartUpV3*: cdn.dns7b.site

🟠 *URL XCIPTV*: http://cdn.dns7b.site/get.php?username=${user}&password=${password}&type=m3u_plus&output=mpegts

🟢 *Link (M3U)*: http://cdn.dns7b.site/get.php?username=${user}&password=${password}&type=m3u_plus&output=mpegts
🟢 *Link Curto (M3U)*: http://e.cdn.dns7b.site/p/${user}/${password}/m3u

🟡 *Link (HLS)*: http://cdn.dns7b.site/get.php?username=${user}&password=${password}&type=m3u_plus&output=hls
🟡 *Link Curto (HLS)*: http://e.cdn.dns7b.site/p/${user}/${password}/hls

🔴 *Link (SSIPTV)*: http://e.cdn.dns7b.site/p/${user}/${password}/ssiptv

📺 *WEB PLAYER*: http://webtv.iptvblinkplayer.com/xtream-code

📺 *IPTV STREAM*: http://cdn.dns7b.site/live/${user}/${password}

🗓️ *Vencimento*: ${expiresDate}
📶 *Conexões*: 1
📦 *Plano*: 👽TESTE IPTV S/ ADULTOS 👽 PRO
💵 *Preço do Plano*: R$ 0,00
🗓️ *Criado em*: ${createdDate}
💳 *Assinar/Renovar Plano*: https://cineflx.topbrr.shop/#/checkout/RXDgQzY1ex/MeWew2A8Ln`;

  return {
    success: true,
    message: "Teste liberado com sucesso! Acesso válido por 24 horas.",
    testUrl: "http://cdn.dns7b.site",
    credentials: {
      username: user.toString(),
      password: password.toString()
    },
    accessDetails: {
      code: user.toString(),
      dnsStb: "cdn.dns7b.site",
      urlXciptv: [`http://cdn.dns7b.site/get.php?username=${user}&password=${password}&type=m3u_plus&output=mpegts`],
      linkM3u: `http://cdn.dns7b.site/get.php?username=${user}&password=${password}&type=m3u_plus&output=mpegts`,
      linkM3uShort: `http://e.cdn.dns7b.site/p/${user}/${password}/m3u`,
      linkHls: `http://cdn.dns7b.site/get.php?username=${user}&password=${password}&type=m3u_plus&output=hls`,
      linkHlsShort: `http://e.cdn.dns7b.site/p/${user}/${password}/hls`,
      linkSsiptv: `http://e.cdn.dns7b.site/p/${user}/${password}/ssiptv`,
      webPlayers: [`http://webtv.iptvblinkplayer.com/xtream-code`],
      iptvStream: `http://cdn.dns7b.site/live/${user}/${password}`,
      expiresAt: expiresDate,
      connections: 1,
      planName: "👽TESTE IPTV S/ ADULTOS 👽 PRO",
      price: "R$ 0,00",
      createdAt: createdDate,
      renewalUrl: "https://cineflx.topbrr.shop/#/checkout/RXDgQzY1ex/MeWew2A8Ln"
    },
    rawResponse: fullFormattedResponse,
    // Manter compatibilidade com o formato antigo
    user: user.toString(),
    password: password.toString(),
    dns: "cdn.dns7b.site",
    package: "👽TESTE IPTV S/ ADULTOS 👽 PRO",
    expirationDate: expiresDate,
    payUrl: "https://cineflx.topbrr.shop/#/checkout/RXDgQzY1ex/MeWew2A8Ln",
    urls: {
      m3u: `http://cdn.dns7b.site/get.php?username=${user}&password=${password}&type=m3u_plus&output=mpegts`,
      hls: `http://cdn.dns7b.site/get.php?username=${user}&password=${password}&type=m3u_plus&output=hls`,
      short_m3u: `http://e.cdn.dns7b.site/p/${user}/${password}/m3u`,
      short_hls: `http://e.cdn.dns7b.site/p/${user}/${password}/hls`,
      ssiptv: `http://e.cdn.dns7b.site/p/${user}/${password}/ssiptv`
    },
    server: {
      host: "cdn.dns7b.site",
      port: "80"
    }
  };
}

const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Proxy server rodando em http://0.0.0.0:${PORT}`);
  console.log(`📱 Acesse do mobile: http://***********:${PORT}`);
  console.log(`🔗 Proxying para: http://localhost:4000`);
  console.log('✅ Servidor iniciado com sucesso!');
});

server.on('error', (err) => {
  console.error('❌ Erro no servidor:', err);
});

// Manter o processo vivo
process.on('SIGINT', () => {
  console.log('\n👋 Proxy server sendo encerrado...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n👋 Proxy server sendo encerrado...');
  process.exit(0);
});
