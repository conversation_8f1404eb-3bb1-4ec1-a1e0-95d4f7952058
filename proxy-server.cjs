const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3001;

// Configurar CORS manualmente para máxima compatibilidade
app.use((req, res, next) => {
  const origin = req.headers.origin;

  // Permitir origens específicas ou localhost
  if (origin && (
    origin.includes('localhost') ||
    origin.includes('127.0.0.1') ||
    origin.includes('***********') ||
    origin.includes('smartv.shop')
  )) {
    res.header('Access-Control-Allow-Origin', origin);
  } else {
    res.header('Access-Control-Allow-Origin', '*');
  }

  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Origin');
  res.header('Access-Control-Allow-Credentials', 'true');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Parse JSON bodies
app.use(express.json());

// Middleware para logs
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url} from ${req.ip}`);
  next();
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// API endpoint para gerar teste IPTV (endpoint original)
app.post('/api/generate-test', async (req, res) => {
  try {
    console.log('🚀 Iniciando geração de teste IPTV...');
    console.log('📥 Request body:', req.body);

    const fetch = (await import('node-fetch')).default;

    const response = await fetch('https://pop.sigma.vin/api/chatbot/e6WnZE7WK8/rlKWO3Wzo7', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      body: JSON.stringify({}),
      timeout: 15000
    });

    if (!response.ok) {
      throw new Error(`API externa retornou status ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ Resposta da API externa recebida:', JSON.stringify(data, null, 2));

    // Processar e estruturar a resposta
    const processedResponse = {
      success: true,
      credentials: {
        username: data.username,
        password: data.password
      },
      accessDetails: {
        planName: data.package || 'IPTV TESTE COMPLETO SEM ADULTOS',
        price: 'R$ 0,00',
        createdAt: data.createdAtFormatted || data.createdAt,
        expiresAt: data.expiresAtFormatted || data.expiresAt,
        connections: data.connections || 1,
        dnsStb: data.dns ? data.dns.replace('http://', '').replace('https://', '') : 'N/A',
        linkM3u: data.dns ? `${data.dns}/get.php?username=${data.username}&password=${data.password}&type=m3u_plus&output=mpegts` : '',
        linkM3uShort: data.dns ? `http://e.${data.dns.replace('http://', '').replace('https://', '')}/p/${data.username}/${data.password}/m3u` : '',
        linkHls: data.dns ? `${data.dns}/get.php?username=${data.username}&password=${data.password}&type=m3u_plus&output=hls` : '',
        linkHlsShort: data.dns ? `http://e.${data.dns.replace('http://', '').replace('https://', '')}/p/${data.username}/${data.password}/hls` : '',
        linkSsiptv: data.dns ? `http://e.${data.dns.replace('http://', '').replace('https://', '')}/p/${data.username}/${data.password}/ssiptv` : '',
        webPlayers: ['http://webtv.iptvblinkplayer.com/'],
        renewalUrl: data.payUrl
      },
      rawResponse: data.reply || JSON.stringify(data, null, 2)
    };

    console.log('📤 Enviando resposta processada:', JSON.stringify(processedResponse, null, 2));
    res.json(processedResponse);

  } catch (error) {
    console.error('❌ Erro ao gerar teste IPTV:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

// API endpoint para teste IPTV (endpoint usado pelo frontend)
app.post('/test/request', async (req, res) => {
  try {
    console.log('🚀 Iniciando geração de teste IPTV via /test/request...');
    console.log('📥 Request body:', req.body);

    const fetch = (await import('node-fetch')).default;

    const response = await fetch('https://pop.sigma.vin/api/chatbot/e6WnZE7WK8/rlKWO3Wzo7', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      body: JSON.stringify({}),
      timeout: 15000
    });

    if (!response.ok) {
      throw new Error(`API externa retornou status ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ Resposta da API externa recebida:', JSON.stringify(data, null, 2));

    // Processar e estruturar a resposta no formato esperado pelo frontend
    const processedResponse = {
      success: true,
      message: "Teste liberado com sucesso! Acesso válido por 4 horas.",
      testUrl: "https://pop.sigma.vin/api/chatbot/e6WnZE7WK8/rlKWO3Wzo7",
      credentials: {
        username: data.username,
        password: data.password
      },
      accessDetails: {
        planName: data.package || 'IPTV TESTE COMPLETO SEM ADULTOS',
        price: 'R$ 0,00',
        createdAt: data.createdAtFormatted || data.createdAt,
        expiresAt: data.expiresAtFormatted || data.expiresAt,
        connections: data.connections || 1,
        dnsStb: data.dns ? data.dns.replace('http://', '').replace('https://', '') : 'N/A',
        linkM3u: data.dns ? `${data.dns}/get.php?username=${data.username}&password=${data.password}&type=m3u_plus&output=mpegts` : '',
        linkM3uShort: data.dns ? `http://e.${data.dns.replace('http://', '').replace('https://', '')}/p/${data.username}/${data.password}/m3u` : '',
        linkHls: data.dns ? `${data.dns}/get.php?username=${data.username}&password=${data.password}&type=m3u_plus&output=hls` : '',
        linkHlsShort: data.dns ? `http://e.${data.dns.replace('http://', '').replace('https://', '')}/p/${data.username}/${data.password}/hls` : '',
        linkSsiptv: data.dns ? `http://e.${data.dns.replace('http://', '').replace('https://', '')}/p/${data.username}/${data.password}/ssiptv` : '',
        webPlayers: ['http://webtv.iptvblinkplayer.com/'],
        renewalUrl: data.payUrl
      },
      rawResponse: data.reply || JSON.stringify(data, null, 2)
    };

    console.log('📤 Enviando resposta processada:', JSON.stringify(processedResponse, null, 2));
    res.json(processedResponse);

  } catch (error) {
    console.error('❌ Erro ao gerar teste IPTV:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Proxy server rodando em http://0.0.0.0:${PORT}`);
  console.log(`📱 Acesse do mobile: http://***********:${PORT}`);
  console.log(`🔗 API IPTV: /api/generate-test`);
});
