import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Tv, Star, Shield, Zap, Play, Users, Heart, Trophy, X, CheckCircle, Clock } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import backend from '~backend/client';
import type { TestRequest, TestResponse } from '~backend/test/test-api';
import type { CreatePixRequest, PixResponse } from '~backend/payment/create-pix';
import Swal from 'sweetalert2';
import {
  showLoadingAlert,
  showSuccessAlert,
  showErrorAlert,
  showTestResultModal
} from '../src/utils/alerts.js';
import '../src/styles/alerts.css';
import WhatsAppButton from '../src/components/WhatsAppButton';

const plans = [
  {
    id: 'basico',
    name: 'Básico',
    price: 19.90,
    description: 'Canais essenciais para toda família',
    features: [
      'Mais de 100 canais',
      'Canais de notícias',
      'Canais de entretenimento',
      'Qualidade HD',
      'Suporte 24h'
    ],
    popular: false,
    icon: Tv
  },
  {
    id: 'criancas',
    name: 'Plano Infantil',
    price: 29.90,
    description: 'Diversão garantida para os pequenos',
    features: [
      'Todos os canais do Básico',
      'Canais infantis premium',
      'Desenhos animados',
      'Conteúdo educativo',
      'Controle parental',
      'Sem propagandas'
    ],
    popular: true,
    icon: Heart
  },
  {
    id: 'familiar-sport',
    name: 'Familiar + Sport',
    price: 34.90,
    description: 'Entretenimento completo + esportes',
    features: [
      'Todos os canais anteriores',
      'Canais de esportes premium',
      'Jogos ao vivo',
      'Filmes e séries',
      'Documentários',
      'Múltiplas telas'
    ],
    popular: false,
    icon: Trophy
  }
];

export default function LandingPage() {
  const navigate = useNavigate();
  const [isTestModalOpen, setIsTestModalOpen] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    plan: ''
  });

  const handlePlanSelect = (plan: (typeof plans)[0]) => {
    const { icon, ...planDetails } = plan;

    // Mostrar modal com formulário integrado
    Swal.fire({
      title: `🎉 Ótima Escolha!`,
      html: `
        <div style="text-align: center; color: #333; padding: 10px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 16px; border-radius: 12px; margin-bottom: 20px;">
            <h3 style="margin: 0 0 8px 0; font-size: 18px;">📺 ${planDetails.name}</h3>
            <p style="margin: 0; font-size: 24px; font-weight: bold;">R$ ${planDetails.price.toFixed(2).replace('.', ',')}</p>
          </div>

          <p style="margin: 16px 0; color: #666; font-size: 16px;">
            Preencha seus dados abaixo para gerar o PIX:
          </p>

          <form id="quickPaymentForm" style="text-align: left; margin-top: 20px;">
            <div style="margin-bottom: 16px;">
              <label style="display: block; margin-bottom: 6px; font-weight: 600; color: #333; font-size: 14px;">Nome Completo:</label>
              <input
                type="text"
                id="modalCustomerName"
                placeholder="Seu nome completo"
                style="width: 100%; padding: 14px 16px; border: 2px solid #e2e8f0; border-radius: 12px; font-size: 15px; box-sizing: border-box; font-family: 'Inter', sans-serif; background: #ffffff; transition: all 0.3s ease; outline: none; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);"
                required
              />
            </div>

            <div style="margin-bottom: 16px;">
              <label style="display: block; margin-bottom: 6px; font-weight: 600; color: #333; font-size: 14px;">E-mail:</label>
              <input
                type="email"
                id="modalCustomerEmail"
                placeholder="<EMAIL>"
                style="width: 100%; padding: 14px 16px; border: 2px solid #e2e8f0; border-radius: 12px; font-size: 15px; box-sizing: border-box; font-family: 'Inter', sans-serif; background: #ffffff; transition: all 0.3s ease; outline: none; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);"
                required
              />
            </div>

            <div style="margin-bottom: 16px;">
              <label style="display: block; margin-bottom: 6px; font-weight: 600; color: #333; font-size: 14px;">CPF:</label>
              <input
                type="text"
                id="modalCustomerDocument"
                placeholder="000.000.000-00"
                maxlength="14"
                style="width: 100%; padding: 14px 16px; border: 2px solid #e2e8f0; border-radius: 12px; font-size: 15px; box-sizing: border-box; font-family: 'Inter', sans-serif; background: #ffffff; transition: all 0.3s ease; outline: none; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);"
                required
              />
            </div>

            <div style="margin-bottom: 20px;">
              <label style="display: block; margin-bottom: 6px; font-weight: 600; color: #333; font-size: 14px;">WhatsApp:</label>
              <input
                type="text"
                id="modalCustomerPhone"
                placeholder="(11) 99999-9999"
                maxlength="15"
                style="width: 100%; padding: 14px 16px; border: 2px solid #e2e8f0; border-radius: 12px; font-size: 15px; box-sizing: border-box; font-family: 'Inter', sans-serif; background: #ffffff; transition: all 0.3s ease; outline: none; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);"
                required
              />
            </div>
          </form>

          <div style="background: #f8f9fa; border-radius: 8px; padding: 12px; margin: 16px 0;">
            <p style="margin: 0; color: #495057; font-size: 13px;">
              💡 <strong>Dica:</strong> Clique em "Usar Exemplo" para preencher automaticamente
            </p>
          </div>
        </div>
      `,
      showCancelButton: true,
      confirmButtonText: '🚀 Gerar PIX',
      cancelButtonText: '📝 Usar Exemplo',
      confirmButtonColor: '#667eea',
      cancelButtonColor: '#28a745',
      width: 'min(95vw, 550px)',
      background: '#ffffff',
      color: '#333333',
      allowOutsideClick: false,
      heightAuto: false,
      customClass: {
        popup: 'pix-modal'
      },
      didOpen: () => {
        // Adicionar máscaras aos campos
        const cpfInput = document.getElementById('modalCustomerDocument') as HTMLInputElement;
        const phoneInput = document.getElementById('modalCustomerPhone') as HTMLInputElement;

        if (cpfInput) {
          cpfInput.addEventListener('input', (e) => {
            const target = e.target as HTMLInputElement;
            let value = target.value.replace(/\\D/g, '');
            value = value.replace(/(\\d{3})(\\d)/, '$1.$2');
            value = value.replace(/(\\d{3})(\\d)/, '$1.$2');
            value = value.replace(/(\\d{3})(\\d{1,2})$/, '$1-$2');
            target.value = value;
          });
        }

        if (phoneInput) {
          phoneInput.addEventListener('input', (e) => {
            const target = e.target as HTMLInputElement;
            let value = target.value.replace(/\\D/g, '');
            value = value.replace(/^(\\d{2})(\\d)/g, '($1) $2');
            value = value.replace(/(\\d{5})(\\d)/, '$1-$2');
            target.value = value;
          });
        }

        // Focar no primeiro campo
        setTimeout(() => {
          const firstInput = document.getElementById('modalCustomerName') as HTMLInputElement;
          if (firstInput) {
            firstInput.focus();
          }
        }, 100);
      },
      preConfirm: () => {
        // Validar e coletar dados do formulário
        const name = (document.getElementById('modalCustomerName') as HTMLInputElement)?.value.trim();
        const email = (document.getElementById('modalCustomerEmail') as HTMLInputElement)?.value.trim();
        const cpfDocument = (document.getElementById('modalCustomerDocument') as HTMLInputElement)?.value.trim();
        const phone = (document.getElementById('modalCustomerPhone') as HTMLInputElement)?.value.trim();

        if (!name || !email || !cpfDocument || !phone) {
          Swal.showValidationMessage('Por favor, preencha todos os campos');
          return false;
        }

        if (name.length < 3) {
          Swal.showValidationMessage('Nome deve ter pelo menos 3 caracteres');
          return false;
        }

        if (!email.includes('@')) {
          Swal.showValidationMessage('E-mail inválido');
          return false;
        }

        if (cpfDocument.replace(/\\D/g, '').length !== 11) {
          Swal.showValidationMessage('CPF deve ter 11 dígitos');
          return false;
        }

        if (phone.replace(/\\D/g, '').length < 10) {
          Swal.showValidationMessage('Telefone inválido');
          return false;
        }

        return { name, email, document: cpfDocument, phone };
      }
    }).then(async (result) => {
      if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
        // Usuário escolheu usar dados de exemplo
        const exampleData = {
          customerName: 'Paulo Antonio Silva',
          customerEmail: '<EMAIL>',
          customerDocument: '123.456.789-00',
          customerPhone: '(11) 99999-9999'
        };

        // Gerar PIX automaticamente após preencher dados de exemplo
        await handlePixGeneration(exampleData, planDetails);

      } else if (result.isConfirmed && result.value) {
        // Usuário preencheu os dados e confirmou
        const { name, email, document, phone } = result.value;

        const formData = {
          customerName: name,
          customerEmail: email,
          customerDocument: document,
          customerPhone: phone
        };

        // Gerar PIX automaticamente
        await handlePixGeneration(formData, planDetails);
      }
    });
  };

  const handlePixGeneration = async (formData: any, planDetails: any) => {
    try {
      // Mostrar loading
      Swal.fire({
        title: 'Gerando PIX...',
        text: 'Aguarde enquanto processamos seu pagamento',
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading();
        }
      });

      const request: CreatePixRequest = {
        customerName: formData.customerName,
        customerEmail: formData.customerEmail,
        customerDocument: formData.customerDocument.replace(/\D/g, ''),
        customerPhone: formData.customerPhone.replace(/\D/g, ''),
        planType: planDetails.name,
        amount: planDetails.price
      };

      const response = await backend.payment.createPix(request);

      // Debug: verificar o que está sendo retornado
      console.log('PIX Response:', response);
      console.log('QR Code URL:', response.qrCodeUrl);

      // Fechar loading e mostrar PIX
      await showPixPopup(response, planDetails);

    } catch (error) {
      console.error('Error creating PIX:', error);
      Swal.fire({
        title: 'Erro ao gerar PIX',
        text: 'Tente novamente em alguns instantes.',
        icon: 'error',
        confirmButtonText: 'OK',
        confirmButtonColor: '#667eea'
      });
    }
  };

  const showPixPopup = async (pixData: PixResponse, planDetails: any) => {
    // Determinar o formato correto da imagem
    let qrCodeImageSrc = '';
    if (pixData.qrCodeUrl) {
      // Se já contém data:image, usar diretamente
      if (pixData.qrCodeUrl.startsWith('data:image/')) {
        qrCodeImageSrc = pixData.qrCodeUrl;
      }
      // Se é base64 puro, adicionar o prefixo
      else if (pixData.qrCodeUrl.length > 100 && !pixData.qrCodeUrl.startsWith('http')) {
        qrCodeImageSrc = `data:image/png;base64,${pixData.qrCodeUrl}`;
      }
      // Se é uma URL, usar diretamente
      else if (pixData.qrCodeUrl.startsWith('http')) {
        qrCodeImageSrc = pixData.qrCodeUrl;
      }
    }

    return Swal.fire({
      title: '💳 Pagamento PIX',
      html: `
        <div style="text-align: center; color: #333; padding: 10px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 16px; border-radius: 12px; margin-bottom: 20px;">
            <h3 style="margin: 0 0 8px 0; font-size: 16px;">💰 Valor: R$ ${planDetails.price.toFixed(2).replace('.', ',')}</h3>
            <p style="margin: 0; font-size: 14px;">${planDetails.name}</p>
          </div>

          <p style="margin: 16px 0; color: #666; font-size: 14px;">
            Escaneie o QR Code ou copie o código PIX
          </p>

          <div style="background: white; padding: 20px; border-radius: 12px; margin: 20px 0; border: 2px solid #e0e0e0; display: flex; justify-content: center; align-items: center; min-height: 200px;">
            ${qrCodeImageSrc ?
              `<img src="${qrCodeImageSrc}" alt="QR Code PIX" style="max-width: 200px; max-height: 200px; width: auto; height: auto;" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';" />
               <div style="display: none; padding: 40px; color: #666; text-align: center;">
                 <p style="margin: 0; font-size: 16px;">📱 QR Code não disponível</p>
                 <p style="margin: 8px 0 0 0; font-size: 12px;">Use o código PIX abaixo</p>
               </div>` :
              `<div style="padding: 40px; color: #666; text-align: center;">
                <p style="margin: 0; font-size: 16px;">📱 QR Code PIX</p>
                <p style="margin: 8px 0 0 0; font-size: 12px;">Use o código abaixo para pagar</p>
              </div>`
            }
          </div>

          <div style="background: #f8f9fa; border-radius: 8px; padding: 12px; margin: 16px 0; word-break: break-all; font-family: monospace; font-size: 12px; color: #495057; max-height: 100px; overflow-y: auto;">
            ${pixData.pixKey}
          </div>

          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 16px; border-radius: 12px; margin: 16px 0;">
            <p style="margin: 0 0 8px 0; font-size: 14px; font-weight: bold;">Como pagar:</p>
            <p style="margin: 0; font-size: 13px;">Abra o app do seu banco</p>
            <p style="margin: 0; font-size: 13px;">Escolha a opção PIX</p>
          </div>
        </div>
      `,
      showCancelButton: true,
      confirmButtonText: '📋 Copiar Código',
      cancelButtonText: '✅ Fechar',
      confirmButtonColor: '#667eea',
      cancelButtonColor: '#28a745',
      width: 'min(95vw, 500px)',
      background: '#ffffff',
      color: '#333333',
      allowOutsideClick: false,
      customClass: {
        popup: 'swal-pix-popup'
      },
      preConfirm: () => {
        // Copiar código PIX
        if (navigator.clipboard) {
          navigator.clipboard.writeText(pixData.pixKey).then(() => {
            Swal.fire({
              title: 'Código copiado!',
              text: 'O código PIX foi copiado para sua área de transferência',
              icon: 'success',
              timer: 2000,
              showConfirmButton: false
            });
          });
        } else {
          // Fallback para navegadores mais antigos
          const textArea = document.createElement('textarea');
          textArea.value = pixData.pixKey;
          document.body.appendChild(textArea);
          textArea.select();
          document.execCommand('copy');
          document.body.removeChild(textArea);

          Swal.fire({
            title: 'Código copiado!',
            text: 'O código PIX foi copiado para sua área de transferência',
            icon: 'success',
            timer: 2000,
            showConfirmButton: false
          });
        }
        return false; // Não fechar o modal
      }
    });
  };

  const handleTestGeneration = async (testData: any) => {
    try {
      // Mostrar loading
      Swal.fire({
        title: 'Gerando Teste...',
        text: 'Aguarde enquanto processamos sua solicitação',
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading();
        }
      });

      // Simular delay de 3 segundos como preferido
      await new Promise(resolve => setTimeout(resolve, 3000));

      const request: TestRequest = {
        name: testData.name,
        email: testData.email,
        plan: testData.planInterest
      };

      const response = await backend.test.requestTest(request);

      // Fechar loading e mostrar resultado
      await showFormattedTestResult(response);

    } catch (error) {
      console.error('Error generating test:', error);
      Swal.fire({
        title: 'Erro ao gerar teste',
        text: 'Tente novamente em alguns instantes.',
        icon: 'error',
        confirmButtonText: 'OK',
        confirmButtonColor: '#667eea'
      });
    }
  };

  const showFormattedTestResult = async (response: any) => {
    console.log('Test Response:', response);

    // Construir dados completos da API real
    let formattedData = '';

    // Verificar se é o formato novo (TestResponse) ou antigo
    const isNewFormat = response.success !== undefined;
    const isOldFormat = response.username && response.password && response.dns;

    if (isNewFormat && response.success) {
      // Usar diretamente o rawResponse que já vem formatado da API externa
      if (response.rawResponse) {
        formattedData = response.rawResponse;
      } else {
        // Fallback: construir manualmente se não houver rawResponse
        formattedData = `✅ Usuário: ${response.credentials?.username || 'N/A'}
✅ Senha: ${response.credentials?.password || 'N/A'}
📦 Plano: ${response.accessDetails?.planName || 'IPTV TESTE COMPLETO SEM ADULTOS'}
💳 Assinar/Renovar Plano: ${response.accessDetails?.renewalUrl || 'N/A'}
💵 Preço do Plano: ${response.accessDetails?.price || 'R$ 0,00'}
🗓️ Criado em: ${response.accessDetails?.createdAt || new Date().toLocaleString('pt-BR')}
🗓️ Vencimento: ${response.accessDetails?.expiresAt || 'N/A'}
📶 Conexões: ${response.accessDetails?.connections || 1}

🟠 DNS XCIPTV: ${response.accessDetails?.dnsStb || 'N/A'}
🟠 DNS SMARTERS: ${response.accessDetails?.dnsStb || 'N/A'}
Usem com 3 barras (SMARTERS)

🟢 Link (M3U): ${response.accessDetails?.linkM3u || 'N/A'}

🟢 Link Curto (M3U): ${response.accessDetails?.linkM3uShort || 'N/A'}

🟡 Link (HLS): ${response.accessDetails?.linkHls || 'N/A'}

🟡 Link Curto (HLS): ${response.accessDetails?.linkHlsShort || 'N/A'}

🔴 Link (SSIPTV): ${response.accessDetails?.linkSsiptv || 'N/A'}

🔗Manual do IPTV
🟢https://www.manualiptv.com/

📺 DNS STB / SmartUp: ${response.accessDetails?.dnsStb || 'N/A'}

📺 WebPlayer: ${response.accessDetails?.webPlayers?.[0] || 'http://webtv.iptvblinkplayer.com/'}

✅ PARA ANDROID:

🟢Aplicativo 3 em 1
🟢https://aftv.news/217356
🟢Código Downloader: 217356

🟡Aplicativo Thunder
🟡https://aftv.news/515800
🟡Código Downloader: 515800

🔴Aplicativo YouTV
🔴https://aftv.news/954983
🔴Código Downloader: 954983`;
      }


    } else {
      // Fallback para formato não reconhecido
      formattedData = response.rawResponse || `❌ Erro ao gerar teste:\n${response.message || 'Erro desconhecido'}`;
    }

    // Usar a função showTestResultModal que já tem formatação estruturada
    return showTestResultModal(formattedData);
  };

  const handleTestRequest = () => {
    navigate('/test');
  };

  const handleTutorialsClick = () => {
    // Função para alternar abas
    const showTutorialTab = (tab: string) => {
      const tutoriaisContent = document.getElementById('content-tutoriais');
      const marcasContent = document.getElementById('content-marcas');
      const tutoriaisTab = document.getElementById('tab-tutoriais');
      const marcasTab = document.getElementById('tab-marcas');

      if (!tutoriaisContent || !marcasContent || !tutoriaisTab || !marcasTab) return;

      // Esconder todos os conteúdos
      tutoriaisContent.style.display = 'none';
      marcasContent.style.display = 'none';

      // Resetar estilos das abas
      tutoriaisTab.style.background = '#e5e7eb';
      tutoriaisTab.style.color = '#374151';
      marcasTab.style.background = '#e5e7eb';
      marcasTab.style.color = '#374151';

      // Mostrar conteúdo selecionado e ativar aba
      if (tab === 'tutoriais') {
        tutoriaisContent.style.display = 'block';
        tutoriaisTab.style.background = '#3b82f6';
        tutoriaisTab.style.color = 'white';
      } else if (tab === 'marcas') {
        marcasContent.style.display = 'block';
        marcasTab.style.background = '#3b82f6';
        marcasTab.style.color = 'white';
      }
    };

    Swal.fire({
      title: '📚 Tutoriais SmartV',
      html: `
        <div style="text-align: left; padding: 5px;">
          <div style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; padding: 12px; border-radius: 10px; margin-bottom: 15px;">
            <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
              <span style="font-size: 16px;">📖</span>
              <span style="font-size: 15px; font-weight: 500;">Guias de Configuração</span>
            </div>
            <div style="font-size: 13px; margin-top: 5px; text-align: center;">Aprenda a configurar seus dispositivos</div>
          </div>

          <!-- Abas de Navegação -->
          <div style="display: flex; margin-bottom: 15px; border-bottom: 2px solid #e5e7eb; gap: 2px;">
            <button id="tab-tutoriais" style="flex: 1; padding: 8px 12px; background: #3b82f6; color: white; border: none; border-radius: 6px 6px 0 0; font-weight: 600; cursor: pointer; transition: all 0.3s ease; font-size: 14px;">
              🎯 Tutoriais
            </button>
            <button id="tab-marcas" style="flex: 1; padding: 8px 12px; background: #e5e7eb; color: #374151; border: none; border-radius: 6px 6px 0 0; font-weight: 600; cursor: pointer; transition: all 0.3s ease; font-size: 14px;">
              📺 Marcas de TV
            </button>
          </div>

          <!-- Conteúdo da Aba Tutoriais -->
          <div id="content-tutoriais" style="background: #f8fafc; border-radius: 8px; padding: 12px;">
            <h4 style="color: #374151; margin-bottom: 12px; font-weight: 600; font-size: 16px;">🎯 Tutoriais Disponíveis:</h4>

            <div style="margin-bottom: 10px;">
              <strong style="color: #059669; font-size: 14px;">📱 Configuração Mobile:</strong>
              <div style="color: #6b7280; font-size: 13px; margin-top: 3px; line-height: 1.4;">
                • XCIPTV (Android/iOS)<br>
                • IPTV Smarters Pro<br>
                • Perfect Player
              </div>
            </div>

            <div style="margin-bottom: 10px;">
              <strong style="color: #059669; font-size: 14px;">📺 Smart TV:</strong>
              <div style="color: #6b7280; font-size: 13px; margin-top: 3px; line-height: 1.4;">
                • Samsung Tizen<br>
                • LG WebOS<br>
                • Android TV
              </div>
            </div>

            <div style="margin-bottom: 10px;">
              <strong style="color: #059669; font-size: 14px;">💻 Computador:</strong>
              <div style="color: #6b7280; font-size: 13px; margin-top: 3px; line-height: 1.4;">
                • VLC Media Player<br>
                • Kodi<br>
                • Web Player
              </div>
            </div>

            <div style="margin-bottom: 10px;">
              <strong style="color: #059669; font-size: 14px;">📡 TV Box:</strong>
              <div style="color: #6b7280; font-size: 13px; margin-top: 3px; line-height: 1.4;">
                • Android TV Box<br>
                • Configuração STB<br>
                • Portal MAG
              </div>
            </div>
          </div>

          <!-- Conteúdo da Aba Marcas de TV -->
          <div id="content-marcas" style="background: #f8fafc; border-radius: 8px; padding: 12px; display: none;">
            <h4 style="color: #374151; margin-bottom: 12px; font-weight: 600; font-size: 16px;">📺 Marcas de TV Compatíveis:</h4>

            <div style="margin-bottom: 8px;">
              <strong style="color: #059669; font-size: 14px;">📱 Samsung:</strong>
              <div style="color: #6b7280; font-size: 12px; margin-top: 2px; line-height: 1.3;">
                • Modelos: QLED, Crystal UHD, The Frame, The Serif<br>
                • Sistema: Tizen OS • ✅ Total
              </div>
            </div>

            <div style="margin-bottom: 8px;">
              <strong style="color: #059669; font-size: 14px;">📱 LG:</strong>
              <div style="color: #6b7280; font-size: 12px; margin-top: 2px; line-height: 1.3;">
                • Modelos: OLED, NanoCell, UHD<br>
                • Sistema: webOS • ✅ Total
              </div>
            </div>

            <div style="margin-bottom: 8px;">
              <strong style="color: #059669; font-size: 14px;">📱 Sony:</strong>
              <div style="color: #6b7280; font-size: 12px; margin-top: 2px; line-height: 1.3;">
                • Modelos: Bravia, 4K, OLED<br>
                • Sistema: Google TV (Android TV) • ✅ Total
              </div>
            </div>

            <div style="margin-bottom: 8px;">
              <strong style="color: #059669; font-size: 14px;">📱 Philips:</strong>
              <div style="color: #6b7280; font-size: 12px; margin-top: 2px; line-height: 1.3;">
                • Modelos: Ambilight, 4K UHD<br>
                • Sistema: Android TV • ✅ Total
              </div>
            </div>

            <div style="margin-bottom: 8px;">
              <strong style="color: #059669; font-size: 14px;">📱 AOC:</strong>
              <div style="color: #6b7280; font-size: 12px; margin-top: 2px; line-height: 1.3;">
                • Modelos: LED, Smart TV 4K<br>
                • Sistema: Android TV • ✅ Total
              </div>
            </div>

            <div style="margin-bottom: 8px;">
              <strong style="color: #059669; font-size: 14px;">📱 TCL:</strong>
              <div style="color: #6b7280; font-size: 12px; margin-top: 2px; line-height: 1.3;">
                • Modelos: P8M, 4K<br>
                • Sistema: Google TV / Roku TV • ✅ Total
              </div>
            </div>

            <div style="margin-bottom: 8px;">
              <strong style="color: #059669; font-size: 14px;">📱 Hisense:</strong>
              <div style="color: #6b7280; font-size: 12px; margin-top: 2px; line-height: 1.3;">
                • Modelos: ULED, 4K<br>
                • Sistema: Vidaa U / Google TV • ✅ Total
              </div>
            </div>

            <div style="margin-bottom: 8px;">
              <strong style="color: #059669; font-size: 14px;">📱 Semp Toshiba:</strong>
              <div style="color: #6b7280; font-size: 12px; margin-top: 2px; line-height: 1.3;">
                • Modelos: LED, Smart TV<br>
                • Sistema: Google TV • ✅ Total
              </div>
            </div>

            <div style="margin-bottom: 8px;">
              <strong style="color: #059669; font-size: 14px;">📱 Gradiente:</strong>
              <div style="color: #6b7280; font-size: 12px; margin-top: 2px; line-height: 1.3;">
                • Modelos: Smart TV Full HD, 4K<br>
                • Sistema: Google TV • ✅ Total
              </div>
            </div>
          </div>

          <div style="color: #666; font-size: 11px; margin-top: 12px; text-align: center; padding: 8px; background: #f1f5f9; border-radius: 6px;">
            💡 Entre em contato via WhatsApp para receber os tutoriais específicos da sua marca
          </div>
        </div>
      `,
      confirmButtonText: '📱 Falar no WhatsApp',
      confirmButtonColor: '#25d366',
      cancelButtonText: '❌ Fechar',
      showCancelButton: true,
      cancelButtonColor: '#6b7280',
      width: 'min(95vw, 600px)',
      heightAuto: false,
      customClass: {
        popup: 'swal-clean-theme tutorials-modal'
      },
      didOpen: () => {
        // Adicionar event listeners após o modal abrir
        const tutoriaisTab = document.getElementById('tab-tutoriais');
        const marcasTab = document.getElementById('tab-marcas');

        if (tutoriaisTab) {
          tutoriaisTab.addEventListener('click', () => showTutorialTab('tutoriais'));
        }
        if (marcasTab) {
          marcasTab.addEventListener('click', () => showTutorialTab('marcas'));
        }
      }
    }).then((result) => {
      if (result.isConfirmed) {
        const message = "Olá! Gostaria de receber os tutoriais de configuração do SmartV para meus dispositivos.";
        const encodedMessage = encodeURIComponent(message);
        const whatsappUrl = `https://wa.me/5541995056052?text=${encodedMessage}`;
        window.open(whatsappUrl, '_blank');
      }
    });
  };

  // Function to open test modal instead of navigating
  const goToTestPage = () => {
    // Mostrar modal com formulário de teste no mesmo padrão do PIX
    Swal.fire({
      title: `🎉 Teste Grátis - SmartV`,
      html: `
        <div style="text-align: center; color: #333; padding: 10px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 16px; border-radius: 12px; margin-bottom: 20px;">
            <h3 style="margin: 0 0 8px 0; font-size: 18px;">⏰ Teste Grátis por 4 Horas</h3>
            <p style="margin: 0; font-size: 14px;">Experimente nossos canais gratuitamente</p>
          </div>

          <p style="margin: 16px 0; color: #666; font-size: 16px;">
            Preencha seus dados abaixo para liberar o teste:
          </p>

          <form id="testForm" style="text-align: left; margin-top: 20px;">
            <div style="margin-bottom: 16px;">
              <label style="display: block; margin-bottom: 6px; font-weight: 600; color: #333; font-size: 14px;">Nome Completo:</label>
              <input
                type="text"
                id="testCustomerName"
                placeholder="Seu nome completo"
                style="width: 100%; padding: 14px 16px; border: 2px solid #e2e8f0; border-radius: 12px; font-size: 15px; box-sizing: border-box; font-family: 'Inter', sans-serif; background: #ffffff; transition: all 0.3s ease; outline: none; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);"
                required
              />
            </div>

            <div style="margin-bottom: 16px;">
              <label style="display: block; margin-bottom: 6px; font-weight: 600; color: #333; font-size: 14px;">E-mail:</label>
              <input
                type="email"
                id="testCustomerEmail"
                placeholder="<EMAIL>"
                style="width: 100%; padding: 14px 16px; border: 2px solid #e2e8f0; border-radius: 12px; font-size: 15px; box-sizing: border-box; font-family: 'Inter', sans-serif; background: #ffffff; transition: all 0.3s ease; outline: none; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);"
                required
              />
            </div>

            <div style="margin-bottom: 20px;">
              <label style="display: block; margin-bottom: 6px; font-weight: 600; color: #333; font-size: 14px;">Plano de Interesse:</label>
              <select
                id="testPlanInterest"
                style="width: 100%; padding: 14px 16px; border: 2px solid #e2e8f0; border-radius: 12px; font-size: 15px; box-sizing: border-box; background: white; font-family: 'Inter', sans-serif; transition: all 0.3s ease; outline: none; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); cursor: pointer; appearance: none; background-image: url('data:image/svg+xml,%3csvg xmlns=\\'http://www.w3.org/2000/svg\\' fill=\\'none\\' viewBox=\\'0 0 20 20\\'%3e%3cpath stroke=\\'%236b7280\\' stroke-linecap=\\'round\\' stroke-linejoin=\\'round\\' stroke-width=\\'1.5\\' d=\\'m6 8 4 4 4-4\\'/%3e%3c/svg%3e'); background-position: right 12px center; background-repeat: no-repeat; background-size: 16px; padding-right: 40px;"
                required
              >
                <option value="">Selecione um plano</option>
                <option value="Básico">📺 Básico - R$ 19,90</option>
                <option value="Premium">🎬 Premium - R$ 29,90</option>
                <option value="Família">👨‍👩‍👧‍👦 Família - R$ 39,90</option>
              </select>
            </div>
          </form>



          <div style="background: #f8f9fa; border-radius: 8px; padding: 12px; margin: 16px 0;">
            <p style="margin: 0; color: #495057; font-size: 13px;">
              💡 <strong>Dica:</strong> Clique em "Usar Exemplo" para preencher automaticamente
            </p>
          </div>
        </div>
      `,
      showCancelButton: true,
      confirmButtonText: '📺 Liberar Teste Grátis',
      cancelButtonText: '📝 Usar Exemplo',
      confirmButtonColor: '#667eea',
      cancelButtonColor: '#28a745',
      width: 'min(95vw, 550px)',
      background: '#ffffff',
      color: '#333333',
      allowOutsideClick: false,
      heightAuto: false,
      customClass: {
        popup: 'test-modal'
      },
      didOpen: () => {
        // Focar no primeiro campo
        setTimeout(() => {
          const firstInput = document.getElementById('testCustomerName') as HTMLInputElement;
          if (firstInput) {
            firstInput.focus();
          }
        }, 100);
      },
      preConfirm: () => {
        // Validar e coletar dados do formulário
        const name = (document.getElementById('testCustomerName') as HTMLInputElement)?.value.trim();
        const email = (document.getElementById('testCustomerEmail') as HTMLInputElement)?.value.trim();
        const planInterest = (document.getElementById('testPlanInterest') as HTMLSelectElement)?.value;

        if (!name || !email || !planInterest) {
          Swal.showValidationMessage('Por favor, preencha todos os campos');
          return false;
        }

        if (name.length < 3) {
          Swal.showValidationMessage('Nome deve ter pelo menos 3 caracteres');
          return false;
        }

        if (!email.includes('@')) {
          Swal.showValidationMessage('E-mail inválido');
          return false;
        }

        return { name, email, planInterest };
      }
    }).then(async (result) => {
      if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
        // Usuário escolheu usar dados de exemplo
        const exampleData = {
          name: 'Paulo Antonio Silva',
          email: '<EMAIL>',
          planInterest: 'Premium'
        };

        // Processar teste com dados de exemplo
        await handleTestGeneration(exampleData);

      } else if (result.isConfirmed && result.value) {
        // Usuário preencheu os dados e confirmou
        const { name, email, planInterest } = result.value;

        const testData = {
          name,
          email,
          planInterest
        };

        // Processar teste
        await handleTestGeneration(testData);
      }
    });
  };

  // Function to close test modal
  const closeTestModal = () => {
    setIsTestModalOpen(false);
    setFormData({ name: '', email: '', plan: '' });
  };

  // Function to handle test form submission
  const handleTestSubmit = async () => {
    if (!formData.name || !formData.email || !formData.plan) {
      Swal.fire({
        title: 'Campos obrigatórios',
        text: 'Por favor, preencha todos os campos.',
        icon: 'warning',
        confirmButtonText: 'OK',
        background: '#ffffff',
        color: '#333333',
        confirmButtonColor: '#667eea',
        customClass: {
          popup: 'swal-clean-theme',
          title: 'swal-clean-title'
        }
      });
      return;
    }

    // Show loading por 3 segundos
    showLoadingAlert('⏳ Gerando Teste', 'Processando sua solicitação...');

    try {
      // Aguardar 3 segundos antes de fazer a requisição
      await new Promise(resolve => setTimeout(resolve, 3000));

      const testRequest: TestRequest = {
        name: formData.name,
        email: formData.email,
        plan: formData.plan
      };

      const response = await backend.test.requestTest(testRequest);

      // Fechar loading
      Swal.close();

      // Construir dados formatados com base na resposta real do backend
      const testData = `
🎯 SEU TESTE IPTV FOI GERADO COM SUCESSO!

👤 CREDENCIAIS DE ACESSO:
✅ Usuário: ${response.user}
✅ Senha: ${response.password}
⏰ Válido até: ${response.expirationDate}

📡 SERVIDOR:
🌐 Host: ${response.server.host}
🔌 Porta: ${response.server.port}

🔗 LINKS DE ACESSO:

🟢 LINK M3U (MPEGTS):
${response.urls.m3u}

🟢 LINK M3U CURTO:
${response.urls.short_m3u}

🟡 LINK HLS:
${response.urls.hls}

🟡 LINK HLS CURTO:
${response.urls.short_hls}

🔴 LINK SSIPTV:
${response.urls.ssiptv}

📺 WEB PLAYERS:
http://webtv.iptvblinkplayer.com/
http://webtv.iptvsmarters.com/

📱 COMO USAR NA SUA TV:
1️⃣ Abra a galeria de aplicativos da sua TV
2️⃣ Pesquise por: IPTV Smarters, TiviMate ou SS IPTV
3️⃣ Baixe o aplicativo de sua preferência
4️⃣ Use as credenciais acima para fazer login

💡 DICAS IMPORTANTES:
• Teste válido por 24 horas
• Funciona em Smart TV, celular, tablet e PC
• Mais de 100 canais disponíveis
• Qualidade HD/Full HD
• Suporte 24h via WhatsApp

🎬 APROVEITE SEU TESTE GRÁTIS!
      `.trim();

      // Usar o padrão organizado para exibir resultado completo
      showTestResultModal(testData).then((result) => {
        if (result.isConfirmed) {
          // Copiar dados para clipboard
          navigator.clipboard.writeText(testData).then(() => {
            showSuccessAlert('📋 Copiado!', 'Dados copiados para a área de transferência');
          }).catch(() => {
            showErrorAlert('❌ Erro', 'Não foi possível copiar os dados');
          });
        }
      });

      // Close the modal
      closeTestModal();

    } catch (error) {
      console.error('Error generating test:', error);

      // Fechar loading
      Swal.close();

      // Usar o padrão organizado para erro
      showErrorAlert('❌ Erro ao Gerar Teste', 'Não foi possível processar sua solicitação. Tente novamente.');
    }
  };


  // Helper function to format the raw response for better display
  const formatRawResponse = (rawResponse: string): string => {
    try {
      // Try to parse as JSON first
      const jsonData = JSON.parse(rawResponse);
      
      // If it has a reply field, extract and format it
      if (jsonData.reply) {
        return formatReplyText(jsonData.reply);
      }
      
      // If it has data array with message, extract and format it
      if (jsonData.data && Array.isArray(jsonData.data) && jsonData.data[0]?.message) {
        return formatReplyText(jsonData.data[0].message);
      }
      
      // Otherwise, return formatted JSON
      return JSON.stringify(jsonData, null, 2);
    } catch (error) {
      // If not JSON, return as is
      return rawResponse;
    }
  };

  // Helper function to format the reply text with proper line breaks and emojis
  const formatReplyText = (text: string): string => {
    return text
      // Decode unicode characters
      .replace(/\\u([0-9a-fA-F]{4})/g, (match, code) => String.fromCharCode(parseInt(code, 16)))
      // Replace \n with actual line breaks
      .replace(/\\n/g, '\n')
      // Remove extra asterisks around text
      .replace(/\*([^*]+)\*/g, '$1')
      // Clean up escaped slashes
      .replace(/\\\//g, '/')
      // Add proper spacing around sections
      .replace(/--------------------------------------/g, '\n--------------------------------------\n')
      .replace(/----------------------------------/g, '\n----------------------------------\n')
      // Clean up multiple line breaks
      .replace(/\n{3,}/g, '\n\n')
      // Trim whitespace
      .trim();
  };

  // Function to show raw response in SweetAlert2
  const showRawResponseAlert = (responseData: TestResponse) => {
    if (!responseData?.rawResponse) return;

    const formattedResponse = formatRawResponse(responseData.rawResponse);
    
    Swal.fire({
      title: 'Teste Completo da API',
      html: `
        <div style="text-align: left; max-height: 400px; overflow-y: auto;">
          <pre style="
            background: rgba(0, 0, 0, 0.2); 
            border: 1px solid rgba(255, 255, 255, 0.1); 
            border-radius: 0.5rem; 
            padding: 1rem; 
            color: #ffffff; 
            font-size: 0.75rem; 
            font-family: 'JetBrains Mono', Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace; 
            white-space: pre-wrap; 
            word-wrap: break-word;
            margin: 0;
          ">${formattedResponse}</pre>
        </div>
      `,
      showCancelButton: true,
      confirmButtonText: 'Copiar Tudo',
      cancelButtonText: 'Fechar',
      background: '#1e293b',
      color: '#ffffff',
      confirmButtonColor: '#a855f7',
      cancelButtonColor: '#6b7280',
      customClass: {
        popup: 'swal-dark-theme swal-wide',
        title: 'swal-title',
        htmlContainer: 'swal-html-container',
        confirmButton: 'swal-confirm-button',
        cancelButton: 'swal-cancel-button'
      },
      width: 'min(90%, 800px)',
      heightAuto: false
    }).then((result) => {
      if (result.isConfirmed) {
        navigator.clipboard.writeText(formattedResponse);
      }
    });
  };

  return (
    <>
      <style>{`
        .swal-payment-popup {
          border-radius: 16px !important;
          box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
        }
        .swal-payment-popup .swal2-title {
          font-size: 24px !important;
          margin-bottom: 10px !important;
        }
        .swal-payment-popup .swal2-html-container {
          margin: 0 !important;
          padding: 0 !important;
          max-height: 70vh !important;
          overflow-y: auto !important;
        }
        .swal-payment-popup .swal2-actions {
          margin-top: 20px !important;
          gap: 12px !important;
          flex-direction: row !important;
        }
        .swal-payment-popup .swal2-confirm,
        .swal-payment-popup .swal2-cancel {
          border-radius: 8px !important;
          padding: 12px 24px !important;
          font-weight: 600 !important;
          font-size: 14px !important;
          min-width: 140px !important;
        }
        .swal-payment-popup input {
          transition: border-color 0.2s ease !important;
        }
        .swal-payment-popup input:focus {
          border-color: #667eea !important;
          outline: none !important;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
        }
        .swal-payment-popup label {
          user-select: none !important;
        }
        .swal-pix-popup {
          border-radius: 16px !important;
          box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
        }
        .swal-pix-popup .swal2-title {
          font-size: 20px !important;
          margin-bottom: 10px !important;
        }
        .swal-pix-popup .swal2-html-container {
          margin: 0 !important;
          padding: 0 !important;
        }
        .swal-pix-popup .swal2-actions {
          margin-top: 20px !important;
          gap: 12px !important;
        }
        .swal-pix-popup .swal2-confirm,
        .swal-pix-popup .swal2-cancel {
          border-radius: 8px !important;
          padding: 12px 24px !important;
          font-weight: 600 !important;
          font-size: 14px !important;
          min-width: 140px !important;
        }
        @media (max-width: 640px) {
          .swal-payment-popup .swal2-actions,
          .swal-pix-popup .swal2-actions {
            flex-direction: column !important;
          }
          .swal-payment-popup .swal2-confirm,
          .swal-payment-popup .swal2-cancel,
          .swal-pix-popup .swal2-confirm,
          .swal-pix-popup .swal2-cancel {
            width: 100% !important;
            margin: 0 !important;
          }
        }
      `}</style>

      <div className="min-h-screen">
      {/* Header */}
      <header className="bg-black/20 backdrop-blur-sm border-b border-white/10 sticky top-0 z-50">
        <div className="container mx-auto px-3 sm:px-4 lg:px-6 py-3 md:py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Tv className="h-5 w-5 sm:h-6 sm:w-6 md:h-8 md:w-8 text-purple-400 flex-shrink-0" />
              <h1 className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold text-white">SmartV</h1>
            </div>
            <Button
              onClick={goToTestPage}
              className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white text-xs sm:text-sm md:text-base px-2 sm:px-3 md:px-4 py-2 md:py-2.5 font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
              <Play className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 flex-shrink-0" />
              <span className="hidden xs:inline">Teste Grátis</span>
              <span className="xs:hidden">Teste</span>
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-8 sm:py-12 md:py-16 lg:py-20 px-3 sm:px-4 lg:px-6">
        <div className="container mx-auto text-center">
          <div className="max-w-5xl mx-auto">
            <h2 className="text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-white mb-4 sm:mb-6 md:mb-8 leading-tight px-2">
              Sua TV
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">
                {" "}Inteligente
              </span>
            </h2>
            <p className="text-sm xs:text-base sm:text-lg md:text-xl lg:text-2xl text-gray-300 mb-6 sm:mb-8 md:mb-10 leading-relaxed px-2 sm:px-4 max-w-4xl mx-auto">
              Acesso a centenas de canais de TV em alta qualidade.
              Entretenimento sem limites para toda a família.
            </p>
            <div className="flex flex-wrap justify-center gap-2 sm:gap-3 md:gap-4 mb-6 sm:mb-8 md:mb-12 px-2 sm:px-4">
              <Badge variant="secondary" className="bg-purple-500/20 text-purple-300 border-purple-400 text-xs sm:text-sm px-2 sm:px-3 py-1">
                <Zap className="h-3 w-3 sm:h-4 sm:w-4 mr-1 flex-shrink-0" />
                <span className="hidden xs:inline">Ativação Imediata</span>
                <span className="xs:hidden">Imediato</span>
              </Badge>
              <Badge variant="secondary" className="bg-green-500/20 text-green-300 border-green-400 text-xs sm:text-sm px-2 sm:px-3 py-1">
                <Shield className="h-3 w-3 sm:h-4 sm:w-4 mr-1 flex-shrink-0" />
                <span className="hidden xs:inline">100% Seguro</span>
                <span className="xs:hidden">Seguro</span>
              </Badge>
              <Badge variant="secondary" className="bg-blue-500/20 text-blue-300 border-blue-400 text-xs sm:text-sm px-2 sm:px-3 py-1">
                <Users className="h-3 w-3 sm:h-4 sm:w-4 mr-1 flex-shrink-0" />
                <span className="hidden xs:inline">Múltiplas Telas</span>
                <span className="xs:hidden">Multi-tela</span>
              </Badge>
            </div>
          </div>
        </div>
      </section>

      {/* Plans Section */}
      <section className="py-8 sm:py-12 md:py-16 lg:py-20 px-3 sm:px-4 lg:px-6">
        <div className="container mx-auto">
          <div className="text-center mb-8 sm:mb-12 md:mb-16">
            <h3 className="text-2xl xs:text-3xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-3 sm:mb-4 md:mb-6 px-2">
              Escolha seu Plano
            </h3>
            <p className="text-sm xs:text-base sm:text-lg md:text-xl lg:text-2xl text-gray-300 px-2 sm:px-4 max-w-3xl mx-auto">
              Planos flexíveis para atender suas necessidades
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8 max-w-7xl mx-auto">
            {plans.map((plan) => {
              const IconComponent = plan.icon;
              return (
                <Card 
                  key={plan.id}
                  className={`relative bg-white/5 backdrop-blur-sm border-white/10 hover:border-purple-400/50 transition-all duration-300 hover:scale-105 ${
                    plan.popular ? 'ring-2 ring-purple-400' : ''
                  }`}
                >
                  {plan.popular && (
                    <div className="absolute -top-3 md:-top-4 left-1/2 transform -translate-x-1/2">
                      <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs md:text-sm px-3 py-1">
                        <Star className="h-3 w-3 mr-1" />
                        Mais Popular
                      </Badge>
                    </div>
                  )}
                  
                  <CardHeader className="text-center p-4 md:p-6">
                    <div className="mx-auto mb-3 md:mb-4 p-2 md:p-3 bg-purple-500/20 rounded-full w-fit">
                      <IconComponent className="h-6 w-6 md:h-8 md:w-8 text-purple-400" />
                    </div>
                    <CardTitle className="text-xl md:text-2xl text-white mb-2">{plan.name}</CardTitle>
                    <CardDescription className="text-gray-300 text-sm md:text-base leading-relaxed">
                      {plan.description}
                    </CardDescription>
                    <div className="text-3xl md:text-4xl font-bold text-white mt-3 md:mt-4">
                      R$ {plan.price.toFixed(2).replace('.', ',')}
                      <span className="text-base md:text-lg text-gray-400">/mês</span>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="p-4 md:p-6 pt-0">
                    <ul className="space-y-2 md:space-y-3 mb-4 md:mb-6">
                      {plan.features.map((feature, index) => (
                        <li key={index} className="flex items-start text-gray-300 text-sm md:text-base">
                          <div className="h-2 w-2 bg-purple-400 rounded-full mr-3 mt-2 flex-shrink-0" />
                          <span className="leading-relaxed">{feature}</span>
                        </li>
                      ))}
                    </ul>
                    
                    <Button 
                      onClick={() => handlePlanSelect(plan)}
                      className="w-full bg-purple-600 hover:bg-purple-700 text-white text-sm md:text-base py-2 md:py-3"
                    >
                      Assinar Agora
                    </Button>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-12 md:py-16 lg:py-20 px-4 bg-black/20">
        <div className="container mx-auto">
          <div className="text-center mb-12 md:mb-16">
            <h3 className="text-3xl md:text-4xl font-bold text-white mb-3 md:mb-4">
              Por que escolher SmartV?
            </h3>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
            {[
              {
                icon: Zap,
                title: 'Ativação Imediata',
                description: 'Comece a assistir em segundos após o pagamento'
              },
              {
                icon: Shield,
                title: 'Totalmente Seguro',
                description: 'Pagamentos protegidos e dados criptografados'
              },
              {
                icon: Users,
                title: 'Múltiplas Telas',
                description: 'Assista em TV, celular, tablet e computador'
              },
              {
                icon: Star,
                title: 'Qualidade Premium',
                description: 'Transmissão em HD com estabilidade garantida'
              }
            ].map((feature, index) => (
              <div key={index} className="text-center p-4">
                <div className="mx-auto mb-3 md:mb-4 p-3 md:p-4 bg-purple-500/20 rounded-full w-fit">
                  <feature.icon className="h-6 w-6 md:h-8 md:w-8 text-purple-400" />
                </div>
                <h4 className="text-lg md:text-xl font-semibold text-white mb-2">
                  {feature.title}
                </h4>
                <p className="text-gray-300 text-sm md:text-base leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-12 md:py-16 lg:py-20 px-4">
        <div className="container mx-auto text-center">
          <div className="max-w-3xl mx-auto">
            <h3 className="text-3xl md:text-4xl font-bold text-white mb-4 md:mb-6">
              Pronto para começar?
            </h3>
            <p className="text-lg md:text-xl text-gray-300 mb-6 md:mb-8 leading-relaxed px-4">
              Teste grátis por 4 horas ou assine agora e tenha acesso imediato
            </p>
            <div className="flex flex-col gap-4 justify-center max-w-sm mx-auto">
              {/* Botão Teste Grátis - Principal e Chamativo */}
              <Button
                size="lg"
                onClick={goToTestPage}
                className="relative bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white w-full py-5 px-8 text-lg font-bold shadow-2xl hover:shadow-xl transition-all duration-300 transform hover:scale-105 animate-pulse hover:animate-none border-2 border-green-400 overflow-hidden group rounded-xl"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-500 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                <div className="relative flex items-center justify-center gap-3">
                  <span className="text-2xl">🎬</span>
                  <div className="flex flex-col">
                    <span className="font-extrabold text-lg">TESTE GRÁTIS</span>
                    <span className="text-sm font-normal opacity-90">4 horas completas</span>
                  </div>
                  <span className="text-2xl animate-bounce">▶</span>
                </div>
              </Button>

              {/* Botão Assinar Agora */}
              <Button
                size="lg"
                onClick={() => handlePlanSelect(plans[1])}
                className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white w-full py-4 px-8 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 rounded-lg"
              >
                <div className="flex items-center justify-center gap-3">
                  <span className="text-xl">💎</span>
                  <span>Assinar Agora</span>
                </div>
              </Button>

              {/* Botão Tutoriais */}
              <Button
                size="lg"
                onClick={handleTutorialsClick}
                className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white w-full py-4 px-8 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 rounded-lg"
              >
                <div className="flex items-center justify-center gap-3">
                  <span className="text-xl">📖</span>
                  <span>Tutoriais</span>
                </div>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black/40 border-t border-white/10 py-4 sm:py-6 md:py-8 px-3 sm:px-4 lg:px-6">
        <div className="container mx-auto text-center">
          <div className="flex items-center justify-center space-x-2 mb-2 sm:mb-3 md:mb-4">
            <Tv className="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6 text-purple-400 flex-shrink-0" />
            <span className="text-base sm:text-lg md:text-xl lg:text-2xl font-bold text-white">SmartV</span>
          </div>
          <p className="text-gray-400 text-xs sm:text-sm md:text-base px-2">
            © 2024 - 2025 SmartV. Todos os direitos reservados.
          </p>
        </div>
      </footer>

      {/* Test Modal - Padronizado igual ao PIX - Responsivo */}
      {isTestModalOpen && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-2 sm:p-4 z-50">
          <div className="bg-white rounded-xl sm:rounded-2xl shadow-2xl w-full max-w-sm sm:max-w-md mx-auto relative max-h-[95vh] overflow-y-auto">
            {/* Close button */}
            <button
              onClick={closeTestModal}
              className="absolute top-3 right-3 sm:top-4 sm:right-4 text-gray-400 hover:text-gray-600 transition-colors z-10"
            >
              <X className="h-5 w-5 sm:h-6 sm:w-6" />
            </button>

            {/* Header - Igual ao PIX */}
            <div className="text-center p-4 sm:p-6 pb-0">
              <div className="flex items-center justify-center mb-3 sm:mb-4">
                <div className="bg-orange-100 p-2 rounded-lg">
                  <Tv className="h-5 w-5 sm:h-6 sm:w-6 text-orange-600" />
                </div>
              </div>
              <h2 className="text-lg sm:text-xl font-bold text-gray-900 mb-3 sm:mb-4">📺 Teste Grátis - SmartV</h2>
            </div>

            {/* Header colorido - Igual ao PIX */}
            <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 sm:px-6 py-3 sm:py-4 mx-4 sm:mx-6 rounded-lg mb-3 sm:mb-4">
              <div className="flex items-center justify-center space-x-2">
                <Clock className="h-4 w-4 sm:h-5 sm:w-5" />
                <span className="font-semibold text-base sm:text-lg">Teste Grátis por 4 Horas</span>
              </div>
              <p className="text-center text-xs sm:text-sm mt-1 opacity-90">Experimente nossos canais gratuitamente</p>
            </div>

            {/* Form */}
            <div className="px-4 sm:px-6 pb-3 sm:pb-4">
              <div className="space-y-3 sm:space-y-4">
                <div>
                  <Label htmlFor="name" className="text-xs sm:text-sm font-medium text-gray-700 block mb-1 sm:mb-2">
                    Nome Completo
                  </Label>
                  <Input
                    id="name"
                    type="text"
                    placeholder="Seu nome completo"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  />
                </div>

                <div>
                  <Label htmlFor="email" className="text-xs sm:text-sm font-medium text-gray-700 block mb-1 sm:mb-2">
                    E-mail
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  />
                </div>

                <div>
                  <Label htmlFor="plan" className="text-xs sm:text-sm font-medium text-gray-700 block mb-1 sm:mb-2">
                    Plano de Interesse
                  </Label>
                  <select
                    id="plan"
                    value={formData.plan}
                    onChange={(e) => setFormData({ ...formData, plan: e.target.value })}
                    className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white"
                  >
                    <option value="">Selecione um plano</option>
                    {plans.map((plan) => (
                      <option key={plan.id} value={plan.id}>
                        {plan.name} - R$ {plan.price.toFixed(2)}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>



            {/* Buttons - Igual ao PIX - Responsivo */}
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 px-4 sm:px-6 pb-4 sm:pb-6">
              <Button
                onClick={handleTestSubmit}
                className="flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white py-2.5 sm:py-3 rounded-lg font-semibold text-sm sm:text-base"
              >
                📺 Liberar Teste Grátis
              </Button>
              <Button
                onClick={closeTestModal}
                className="px-4 sm:px-6 py-2.5 sm:py-3 bg-gray-500 hover:bg-gray-600 text-white rounded-lg font-semibold text-sm sm:text-base"
              >
                ✅ Fechar
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Botão flutuante do WhatsApp */}
      <WhatsAppButton
        phoneNumber="5541995056052"
        message="Olá! Gostaria de saber mais sobre os planos SmartV."
      />

      </div>
    </>
  );
}
